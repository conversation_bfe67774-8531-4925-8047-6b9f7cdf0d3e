import request from '@/config/axios'
import { getAccessToken } from '@/utils/auth'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { config } from '@/config/axios/config'

// AI 评估分析请求 VO
export interface AiEvaluationAnalysisReqVO {
  modelId: number // 模型ID
  content: string // 分析内容
  evaluationResultId?: number // 评估结果ID
  stream?: boolean // 是否流式响应
}

// AI 评估分析响应 VO
export interface AiEvaluationAnalysisRespVO {
  content: string // 分析内容
  status: string // 分析状态
  finished: boolean // 是否完成
}

// AI 评估分析请求 VO (新增，用于兼容前端直连模式)
export interface AiEvalAnalysisReqVO {
  query: string // 分析内容
  elderInfo?: string // 老人信息
  user?: string // 用户标识
  conversationId?: string // 对话ID
  responseMode?: string // 响应模式
}

// AI 评估分析响应 VO (新增，用于兼容前端直连模式)
export interface AiEvalAnalysisRespVO {
  event: string // 事件类型
  taskId?: string // 任务ID
  answer?: string // 回答内容
  status: string // 分析状态
  finished: boolean // 是否完成
  error?: string // 错误信息
}

// AI 评估分析 API
export const AiEvaluationApi = {
  // 生成评估分析（流式）
  generateAnalysisStream: ({
    data,
    onMessage,
    onError,
    onClose,
    ctrl
  }: {
    data: AiEvaluationAnalysisReqVO
    onMessage?: (res: any) => void
    onError?: (...args: any[]) => void
    onClose?: (...args: any[]) => void
    ctrl: AbortController
  }) => {
    const token = getAccessToken()
    return fetchEventSource(`${config.base_url}/ai/evaluation/analysis-stream`, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      openWhenHidden: true,
      body: JSON.stringify(data),
      onmessage: onMessage,
      onerror: onError,
      onclose: onClose,
      signal: ctrl.signal
    })
  },

  // 生成评估分析（非流式）
  generateAnalysis: async (data: AiEvaluationAnalysisReqVO) => {
    return await request.post({ url: `/ai/evaluation/analysis`, data })
  },

  // 生成评估AI分析（流式）- 新增，兼容前端直连模式
  generateEvalAnalysisStream: ({
    data,
    onMessage,
    onError,
    onClose,
    ctrl
  }: {
    data: AiEvalAnalysisReqVO
    onMessage?: (res: any) => void
    onError?: (...args: any[]) => void
    onClose?: (...args: any[]) => void
    ctrl: AbortController
  }) => {
    const token = getAccessToken()
    return fetchEventSource(`${config.base_url}/ai/evaluation/eval-analysis-stream`, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      openWhenHidden: true,
      body: JSON.stringify(data),
      onmessage: onMessage,
      onerror: onError,
      onclose: onClose,
      signal: ctrl.signal
    })
  },

  // 停止AI分析 - 新增
  stopEvalAnalysis: async (taskId: string) => {
    return await request.post({ url: `/ai/evaluation/eval-analysis-stop/${taskId}` })
  },

  // 生成单表评估AI分析（流式）- 根据模板ID获取API Key
  generateTemplateAnalysisStream: ({
    data,
    templateId,
    onMessage,
    onError,
    onClose,
    ctrl
  }: {
    data: AiEvalAnalysisReqVO
    templateId: number
    onMessage?: (res: any) => void
    onError?: (...args: any[]) => void
    onClose?: (...args: any[]) => void
    ctrl: AbortController
  }) => {
    const token = getAccessToken()

    // 简单的错误处理包装函数
    const enhancedOnError = (error: any) => {
      console.error('模板AI分析流式请求错误:', error)

      // 增强错误信息
      if (error.status) {
        error.message = `HTTP ${error.status}: ${error.statusText || '请求失败'}`
      } else if (error.type === 'error') {
        error.message = '网络连接失败'
        error.type = 'NETWORK_ERROR'
      }

      if (onError) {
        onError(error)
      }
    }

    return fetchEventSource(
      `${config.base_url}/ai/evaluation/template-analysis-stream?templateId=${templateId}`,
      {
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        openWhenHidden: true,
        body: JSON.stringify(data),
        onmessage: onMessage,
        onerror: enhancedOnError,
        onclose: onClose,
        signal: ctrl.signal
      }
    )
  },

  // 生成清单评估AI分析（流式）- 根据清单ID获取API Key
  generateListAnalysisStream: ({
    data,
    listId,
    onMessage,
    onError,
    onClose,
    ctrl
  }: {
    data: AiEvalAnalysisReqVO
    listId: number
    onMessage?: (res: any) => void
    onError?: (...args: any[]) => void
    onClose?: (...args: any[]) => void
    ctrl: AbortController
  }) => {
    const token = getAccessToken()

    // 简单的错误处理包装函数
    const enhancedOnError = (error: any) => {
      console.error('清单AI分析流式请求错误:', error)

      // 增强错误信息
      if (error.status) {
        error.message = `HTTP ${error.status}: ${error.statusText || '请求失败'}`
      } else if (error.type === 'error') {
        error.message = '网络连接失败'
        error.type = 'NETWORK_ERROR'
      }

      if (onError) {
        onError(error)
      }
    }

    return fetchEventSource(
      `${config.base_url}/ai/evaluation/list-analysis-stream?listId=${listId}`,
      {
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        openWhenHidden: true,
        body: JSON.stringify(data),
        onmessage: onMessage,
        onerror: enhancedOnError,
        onclose: onClose,
        signal: ctrl.signal
      }
    )
  }
}
