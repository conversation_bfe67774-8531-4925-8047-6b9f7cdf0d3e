import request from '@/config/axios'

// 老人基础信息 VO
export interface ArchivesProfileVO {
  id: number // 老人ID
  name: string // 姓名
  idNumber: string // 身份证号
  gender: number // 性别
  birthDate: Date // 出生日期
  height: number // 身高(cm)
  weight: number // 体重(kg)
  contactPhone: string // 联系电话
  avatar: string // 头像
  ethnicGroup: number // 民族
  religiousBelief: number // 宗教信仰
  serviceLevel: number // 服务等级
  healthLevel: number // 健康等级
  educationLevel: number // 文化程度
  maritalStatus: number // 婚姻状况
  emergencyContact: string // 紧急联系人姓名
  emergencyPhone: string // 紧急联系人电话
  emergencyRelation: number // 与紧急联系人关系
  householdType: number // 户籍类型
  residenceType: string // 居住类型(多选,以逗号分隔)
  medicalInsurance: string // 医疗保险(多选,以逗号分隔)
  occupationType: number // 职业类型
  incomeSource: string // 经济来源(多选,以逗号分隔)
  monthlyIncome: number // 月收入(元)
  archiveStatus: number // 档案建设状态
  remark: string // 备注
  exporting?: boolean // 导出状态（前端使用）
}

// 添加老人精简信息 VO
export interface ArchivesProfileSimpleVO {
  id: number
  name: string
}

// 老人基础信息 API
export const ArchivesProfileApi = {
  // 查询老人基础信息分页
  getArchivesProfilePage: async (params: any) => {
    return await request.get({ url: `/elderArchives/archives-profile/page`, params })
  },

  // 查询老人基础信息详情
  getArchivesProfile: async (id: number) => {
    return await request.get({ url: `/elderArchives/archives-profile/get?id=` + id })
  },

  // 新增老人基础信息
  createArchivesProfile: async (data: ArchivesProfileVO) => {
    return await request.post({ url: `/elderArchives/archives-profile/create`, data })
  },

  // 修改老人基础信息
  updateArchivesProfile: async (data: ArchivesProfileVO) => {
    return await request.put({ url: `/elderArchives/archives-profile/update`, data })
  },

  // 删除老人基础信息
  deleteArchivesProfile: async (id: number) => {
    return await request.delete({ url: `/elderArchives/archives-profile/delete?id=` + id })
  },

  // 检查老人是否可以删除
  checkElderDeletable: async (id: number) => {
    return await request.get({ url: `/elderArchives/archives-profile/check-deletable?id=` + id })
  },

  // 导出老人基础信息 Excel
  exportArchivesProfile: async (params) => {
    return await request.download({ url: `/elderArchives/archives-profile/export-excel`, params })
  },

  // 获取老人精简信息列表
  getArchivesProfileSimpleList: async () => {
    return await request.get<ArchivesProfileSimpleVO[]>({
      url: '/elderArchives/archives-profile/list-all-simple'
    })
  },

  // 上传头像
  uploadAvatar(data: FormData) {
    return request.post({
      url: '/elderArchives/archives-profile/upload-avatar',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}