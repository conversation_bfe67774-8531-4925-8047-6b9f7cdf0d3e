import request from '@/config/axios'

export interface BedVO {
  id?: number
  buildingId?: number
  buildingName: string
  floor?: number
  roomId?: number
  roomNumber: string
  bedNumber: string
  bedType?: number
  status: number
  occupantId?: number
  occupantName?: string
  description?: string
  createTime?: Date
}

export interface BedPageReqVO extends PageParam {
  roomId?: number
  buildingId?: number
  floor?: number
  bedNumber?: string
  bedType?: number
  status?: number
}

// 查询床铺列表
export const getBedList = (params: BedPageReqVO) => {
  return request.get({ url: '/institution/bed/list', params })
}

// 查询床铺详情
export const getBed = (id: number) => {
  return request.get({ url: '/institution/bed/get?id=' + id })
}

// 新增床铺
export const createBed = (data: BedVO) => {
  return request.post({ url: '/institution/bed/create', data })
}

// 修改床铺
export const updateBed = (data: BedVO) => {
  return request.put({ url: '/institution/bed/update', data })
}

// 删除床铺
export const deleteBed = (id: number) => {
  return request.delete({ url: '/institution/bed/delete?id=' + id })
}

// 根据房间ID查询床铺列表
export const getBedListByRoomId = (roomId: number) => {
  return request.get({ url: '/institution/bed/list-by-room?roomId=' + roomId })
}

// 床铺服务对象
export const BedService = {
  getBedList,
  getBed,
  createBed,
  updateBed,
  deleteBed,
  getBedListByRoomId
}
