import request from '@/config/axios'

export interface BuildingVO {
  id?: number
  name: string
  floorCount: number
  address?: string
  description?: string
  createTime?: Date
}

export interface BuildingPageReqVO extends PageParam {
  name?: string
}

// 查询楼宇列表
export const getBuildingList = (params: BuildingPageReqVO) => {
  return request.get({ url: '/institution/building/list', params })
}

// 查询楼宇详情
export const getBuilding = (id: number) => {
  return request.get({ url: '/institution/building/get?id=' + id })
}

// 新增楼宇
export const createBuilding = (data: BuildingVO) => {
  return request.post({ url: '/institution/building/create', data })
}

// 修改楼宇
export const updateBuilding = (data: BuildingVO) => {
  return request.put({ url: '/institution/building/update', data })
}

// 删除楼宇
export const deleteBuilding = (id: number) => {
  return request.delete({ url: '/institution/building/delete?id=' + id })
}

// 导出楼宇 Excel
export const exportBuilding = (params: BuildingPageReqVO) => {
  return request.download({ url: '/institution/building/export-excel', params })
}

// 导出所有API
export const BuildingService = {
  getBuildingList,
  getBuilding,
  createBuilding,
  updateBuilding,
  deleteBuilding,
  exportBuilding
}