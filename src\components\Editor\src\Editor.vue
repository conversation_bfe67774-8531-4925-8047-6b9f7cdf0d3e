<script lang="ts" setup>
import { PropType } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { i18nChangeLanguage, IDomEditor, IEditorConfig } from '@wangeditor/editor'
import { propTypes } from '@/utils/propTypes'
import { isNumber } from '@/utils/is'
import { ElMessage } from 'element-plus'
import { useLocaleStore } from '@/store/modules/locale'
import { getRefreshToken, getTenantId } from '@/utils/auth'
import { getUploadUrl } from '@/components/UploadFile/src/useUpload'

defineOptions({ name: 'Editor' })

type InsertFnType = (url: string, alt: string, href: string) => void

const localeStore = useLocaleStore()

const currentLocale = computed(() => localeStore.getCurrentLocale)

i18nChangeLanguage(unref(currentLocale).lang)

const props = defineProps({
  editorId: propTypes.string.def('wangeEditor-1'),
  height: propTypes.oneOfType([Number, String]).def('500px'),
  editorConfig: {
    type: Object as PropType<Partial<IEditorConfig>>,
    default: () => undefined
  },
  readonly: propTypes.bool.def(false),
  modelValue: propTypes.string.def('')
})

const emit = defineEmits(['change', 'update:modelValue'])

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef<IDomEditor>()

const valueHtml = ref('')

watch(
  () => props.modelValue,
  (val: string) => {
    if (val === unref(valueHtml)) return
    valueHtml.value = val
  },
  {
    immediate: true
  }
)

// 监听
watch(
  () => valueHtml.value,
  (val: string) => {
    emit('update:modelValue', val)
  }
)

const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor
}

// 编辑器配置
const editorConfig = computed((): IEditorConfig => {
  // 获取用户传入的配置
  const userConfig = props.editorConfig || {}

  // 默认基础配置
  const defaultConfig = {
    placeholder: '请输入内容...',
    readOnly: props.readonly,
    customAlert: (s: string, t: string) => {
      switch (t) {
        case 'success':
          ElMessage.success(s)
          break
        case 'info':
          ElMessage.info(s)
          break
        case 'warning':
          ElMessage.warning(s)
          break
        case 'error':
          ElMessage.error(s)
          break
        default:
          ElMessage.info(s)
          break
      }
    },
    autoFocus: false,
    scroll: true,
    uploadImgShowBase64: true
  }

  // 检查用户是否提供了自定义的 MENU_CONF
  const userMenuConf = userConfig.MENU_CONF || {}

  // 默认的图片上传配置（兼容原始配置和新的自定义上传）
  const defaultImageConfig = userMenuConf.uploadImage?.server ? {
    // 如果用户提供了 server 配置，使用原始的服务器上传方式
    server: getUploadUrl(),
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxNumberOfFiles: 10,
    allowedFileTypes: ['image/*'],
    headers: {
      Accept: '*',
      Authorization: 'Bearer ' + getRefreshToken(),
      'tenant-id': getTenantId()
    },
    timeout: 15 * 1000, // 15秒
    fieldName: 'file',
    onBeforeUpload(file: File) {
      return file
    },
    onProgress(progress: number) {
      console.log('progress', progress)
    },
    onSuccess(file: File, res: any) {
      console.log('onSuccess', file, res)
    },
    onFailed(file: File, res: any) {
      alert(res.message)
      console.log('onFailed', file, res)
    },
    onError(file: File, err: any, res: any) {
      alert(err.message)
      console.error('onError', file, err, res)
    },
    customInsert(res: any, insertFn: InsertFnType) {
      insertFn(res.data, 'image', res.data)
    },
    ...userMenuConf.uploadImage // 合并用户的图片上传配置
  } : {
    // 使用自定义上传，完全控制上传流程
    async customUpload(file: File, insertFn: InsertFnType) {
      // 立即检查文件大小
      const maxSize = 2 * 1024 * 1024 // 2MB
      const fileSizeMB = (file.size / 1024 / 1024).toFixed(1)

      console.log(`图片上传开始: 文件名=${file.name}, 大小=${fileSizeMB}MB, 类型=${file.type}`)

      if (file.size > maxSize) {
        // 立即显示错误提示
        ElMessage.error(`上传图片大于${fileSizeMB}MB，支持上传图片（最大2MB）`)
        return
      }

      // 显示上传进度提示
      const loadingMessage = ElMessage({
        message: `图片上传中... (${fileSizeMB}MB)`,
        type: 'info',
        duration: 0, // 不自动关闭
        showClose: false
      })

      try {
        // 创建 FormData
        const formData = new FormData()
        formData.append('file', file)

        console.log('开始发送上传请求...')

        // 使用 XMLHttpRequest 替代 fetch，避免 AbortController 问题
        const response = await new Promise((resolve, reject) => {
          const xhr = new XMLHttpRequest()

          // 设置超时
          xhr.timeout = 60000 // 60秒超时

          // 监听上传进度
          xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
              const percentComplete = (event.loaded / event.total) * 100
              console.log(`上传进度: ${percentComplete.toFixed(1)}%`)
            }
          })

          // 监听状态变化
          xhr.onreadystatechange = () => {
            if (xhr.readyState === XMLHttpRequest.DONE) {
              if (xhr.status >= 200 && xhr.status < 300) {
                resolve({
                  ok: true,
                  status: xhr.status,
                  json: () => Promise.resolve(JSON.parse(xhr.responseText)),
                  text: () => Promise.resolve(xhr.responseText)
                })
              } else {
                reject(new Error(`HTTP error! status: ${xhr.status}, response: ${xhr.responseText}`))
              }
            }
          }

          // 监听错误
          xhr.onerror = () => reject(new Error('网络错误'))
          xhr.ontimeout = () => reject(new Error('上传超时'))
          xhr.onabort = () => reject(new Error('上传被中止'))

          // 发送请求
          xhr.open('POST', getUploadUrl())
          xhr.setRequestHeader('Authorization', 'Bearer ' + getRefreshToken())
          xhr.setRequestHeader('tenant-id', getTenantId())
          xhr.send(formData)
        })

        // 关闭加载提示
        loadingMessage.close()

        console.log('上传请求响应状态:', (response as any).status)

        if (!(response as any).ok) {
          const errorText = await (response as any).text()
          console.error('HTTP错误响应内容:', errorText)
          throw new Error(`HTTP error! status: ${(response as any).status}, message: ${errorText}`)
        }

        const result = await (response as any).json()
        console.log('上传响应结果:', result)

        // 兼容不同的响应格式：errno 或 code
        if (result.errno === 0 || result.code === 0) {
          // 上传成功
          ElMessage.success('图片上传成功')
          // 兼容不同的数据格式
          const imageUrl = result.data?.url || result.data || ''
          const imageAlt = result.data?.alt || ''
          const imageHref = result.data?.href || ''

          if (!imageUrl) {
            console.error('警告: 上传成功但未获取到图片URL', result)
            ElMessage.warning('上传成功但未获取到图片地址')
            return
          }

          console.log('插入图片:', { imageUrl, imageAlt, imageHref })
          insertFn(imageUrl, imageAlt, imageHref)
        } else {
          // 上传失败
          console.error('上传失败，服务器返回:', result)
          ElMessage.error(result.message || result.msg || '图片上传失败')
        }
      } catch (error: any) {
        // 关闭加载提示
        loadingMessage.close()

        console.error('图片上传错误:', error)

        if (error.message.includes('上传超时')) {
          ElMessage.error('图片上传超时，请检查网络连接')
        } else if (error.message.includes('网络错误')) {
          ElMessage.error('网络连接失败，请检查网络后重试')
        } else if (error.message.includes('上传被中止')) {
          ElMessage.error('图片上传被中止，请重试')
        } else if (error.name === 'AbortError') {
          ElMessage.error('图片上传被意外中止，可能是浏览器扩展或网络问题导致，请重试')
        } else {
          ElMessage.error(`图片上传失败: ${error.message}`)
        }
      }
    },
    ...userMenuConf.uploadImage // 合并用户的图片上传配置
  }

  // 默认的视频上传配置（兼容原始配置和新的自定义上传）
  const defaultVideoConfig = userMenuConf.uploadVideo?.server ? {
    // 如果用户提供了 server 配置，使用原始的服务器上传方式
    server: getUploadUrl(),
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxNumberOfFiles: 10,
    allowedFileTypes: ['video/*'],
    headers: {
      Accept: '*',
      Authorization: 'Bearer ' + getRefreshToken(),
      'tenant-id': getTenantId()
    },
    timeout: 15 * 1000, // 15秒
    fieldName: 'file',
    onBeforeUpload(file: File) {
      return file
    },
    onProgress(progress: number) {
      console.log('progress', progress)
    },
    onSuccess(file: File, res: any) {
      console.log('onSuccess', file, res)
    },
    onFailed(file: File, res: any) {
      alert(res.message)
      console.log('onFailed', file, res)
    },
    onError(file: File, err: any, res: any) {
      alert(err.message)
      console.error('onError', file, err, res)
    },
    customInsert(res: any, insertFn: InsertFnType) {
      insertFn(res.data, 'mp4', res.data)
    },
    ...userMenuConf.uploadVideo // 合并用户的视频上传配置
  } : {
    // 使用自定义上传，完全控制上传流程
    async customUpload(file: File, insertFn: InsertFnType) {
      // 立即检查文件大小
      const maxSize = 10 * 1024 * 1024 // 10MB (视频保持10MB限制)
      const fileSizeMB = (file.size / 1024 / 1024).toFixed(1)

      console.log(`视频上传开始: 文件名=${file.name}, 大小=${fileSizeMB}MB, 类型=${file.type}`)

      if (file.size > maxSize) {
        // 立即显示错误提示
        ElMessage.error(`上传视频大于${fileSizeMB}MB，支持上传视频（最大10MB）`)
        return
      }

      // 显示上传进度提示
      const loadingMessage = ElMessage({
        message: `视频上传中... (${fileSizeMB}MB)`,
        type: 'info',
        duration: 0, // 不自动关闭
        showClose: false
      })

      try {
        // 创建 FormData
        const formData = new FormData()
        formData.append('file', file)

        console.log('开始发送视频上传请求...')

        // 发起上传请求，添加超时控制
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 60000) // 60秒超时（视频文件较大）

        const response = await fetch(getUploadUrl(), {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer ' + getRefreshToken(),
            'tenant-id': getTenantId()
          },
          body: formData,
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        // 关闭加载提示
        loadingMessage.close()

        console.log('视频上传请求响应状态:', response.status)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        console.log('视频上传响应结果:', result)

        // 兼容不同的响应格式：errno 或 code
        if (result.errno === 0 || result.code === 0) {
          // 上传成功
          ElMessage.success('视频上传成功')
          // 兼容不同的数据格式
          const videoUrl = result.data?.url || result.data || ''
          const videoPoster = result.data?.poster || ''
          insertFn(videoUrl, videoPoster, videoUrl)
        } else {
          // 上传失败
          ElMessage.error(result.message || result.msg || '视频上传失败')
        }
      } catch (error: any) {
        // 关闭加载提示
        loadingMessage.close()

        console.error('视频上传错误:', error)

        if (error.name === 'AbortError') {
          ElMessage.error('视频上传超时，请检查网络连接或文件大小')
        } else if (error.message.includes('Failed to fetch')) {
          ElMessage.error('网络连接失败，请检查网络后重试')
        } else {
          ElMessage.error(`视频上传失败: ${error.message}`)
        }
      }
    },
    ...userMenuConf.uploadVideo // 合并用户的视频上传配置
  }

  // 合并所有配置
  return Object.assign(
    defaultConfig,
    {
      MENU_CONF: {
        uploadImage: defaultImageConfig,
        uploadVideo: defaultVideoConfig,
        ...userMenuConf // 合并用户的其他菜单配置
      }
    },
    userConfig // 最后合并用户的所有配置，确保用户配置优先级最高
  )
})

const editorStyle = computed(() => {
  return {
    height: isNumber(props.height) ? `${props.height}px` : props.height
  }
})

// 回调函数
const handleChange = (editor: IDomEditor) => {
  emit('change', editor)
}

// 组件销毁时，及时销毁编辑器
onBeforeUnmount(() => {
  const editor = unref(editorRef.value)

  // 销毁，并移除 editor
  editor?.destroy()
})

const getEditorRef = async (): Promise<IDomEditor> => {
  await nextTick()
  return unref(editorRef.value) as IDomEditor
}

defineExpose({
  getEditorRef
})
</script>

<template>
  <div class="border-1 border-solid border-[var(--tags-view-border-color)] z-10">
    <!-- 工具栏 -->
    <Toolbar :editor="editorRef" :editorId="editorId"
      class="border-0 b-b-1 border-solid border-[var(--tags-view-border-color)]" />
    <!-- 编辑器 -->
    <Editor v-model="valueHtml" :defaultConfig="editorConfig" :editorId="editorId" :style="editorStyle"
      @on-change="handleChange" @on-created="handleCreated" />
  </div>
</template>

<style src="@wangeditor/editor/dist/css/style.css"></style>
