<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="关联的老人ID" prop="elderId">
        <el-input v-model="formData.elderId" placeholder="请输入关联的老人ID" />
      </el-form-item>
      <el-form-item label="事件类型" prop="eventType">
        <el-select v-model="formData.eventType" placeholder="请选择事件类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CARE_RISK_EVENT_EVENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="事件等级" prop="eventLevel">
        <el-select v-model="formData.eventLevel" placeholder="请选择事件等级">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CARE_RISK_EVENT_EVENT_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="记录日期" prop="recordDate">
        <el-date-picker
          v-model="formData.recordDate"
          type="date"
          value-format="x"
          placeholder="选择记录日期"
        />
      </el-form-item>
      <el-form-item label="记录人员" prop="recorder">
        <el-input v-model="formData.recorder" placeholder="请输入记录人员" />
      </el-form-item>
      <el-form-item label="事件描述" prop="description">
        <Editor v-model="formData.description" height="150px" />
        <div class="upload-tip">
          <el-text type="info" size="small">
            <el-icon><InfoFilled /></el-icon>
            支持上传图片（最大2MB）和视频（最大10MB）
          </el-text>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { CareRiskEventApi, CareRiskEventVO } from '@/api/elderArchives/careriskevent'
import { InfoFilled } from '@element-plus/icons-vue'

/** 老人照护风险事件 表单 */
defineOptions({ name: 'CareRiskEventForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  elderId: undefined,
  eventType: undefined,
  eventLevel: undefined,
  recordDate: undefined,
  recorder: undefined,
  description: undefined,
})
const formRules = reactive({
  elderId: [{ required: true, message: '关联的老人ID不能为空', trigger: 'blur' }],
  eventType: [{ required: true, message: '事件类型不能为空', trigger: 'change' }],
  eventLevel: [{ required: true, message: '事件等级不能为空', trigger: 'change' }],
  recordDate: [{ required: true, message: '记录日期不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CareRiskEventApi.getCareRiskEvent(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CareRiskEventVO
    if (formType.value === 'create') {
      await CareRiskEventApi.createCareRiskEvent(data)
      message.success(t('common.createSuccess'))
    } else {
      await CareRiskEventApi.updateCareRiskEvent(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    elderId: undefined,
    eventType: undefined,
    eventLevel: undefined,
    recordDate: undefined,
    recorder: undefined,
    description: undefined,
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.upload-tip {
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.upload-tip .el-icon {
  font-size: 14px;
}
</style>
