<template>
  <el-dialog
    v-model="dialogVisible"
    title="签约信息详情"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-descriptions :column="2" border>
      <el-descriptions-item label="老人姓名">{{ detailData.elderName || '未知' }}</el-descriptions-item>
      <el-descriptions-item label="签约员工">{{ detailData.employeeNames || '未知' }}</el-descriptions-item>
      <el-descriptions-item label="签约日期">{{ formatDateArray(detailData.contractDate) }}</el-descriptions-item>
      <el-descriptions-item label="签约类型">
        <dict-tag :type="DICT_TYPE.ELDER_CONTRACT_INFO_CONTRACT_TYPE" :value="detailData.contractType" />
      </el-descriptions-item>
      <el-descriptions-item label="签约原因">{{ detailData.contractReason || '无' }}</el-descriptions-item>
      <el-descriptions-item label="签约期限">{{ detailData.contractPeriod }}个月</el-descriptions-item>
      <el-descriptions-item label="开始日期">{{ formatDateArray(detailData.startDate) }}</el-descriptions-item>
      <el-descriptions-item label="结束日期">{{ formatDateArray(detailData.endDate) }}</el-descriptions-item>
      <el-descriptions-item label="签约状态">
        <dict-tag :type="DICT_TYPE.ELDER_CONTRACT_INFO_CONTRACT_STATUS" :value="detailData.contractStatus" />
      </el-descriptions-item>
      <el-descriptions-item label="终止原因">{{ detailData.terminationReason || '无' }}</el-descriptions-item>
      <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
<!--      <el-descriptions-item label="创建时间" :span="2">{{ formatDate(detailData.createTime) }}</el-descriptions-item>-->
    </el-descriptions>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { ContractInfoVO } from '@/api/elderArchives/contractinfo'

/** 签约信息详情 */
defineOptions({ name: 'ContractInfoDetail' })

// 扩展ContractInfoVO类型，添加elderName属性
interface ContractInfoWithElderName extends ContractInfoVO {
  elderName?: string
  employeeNames?: string
}

const dialogVisible = ref(false) // 弹窗的是否展示
const detailData = ref<ContractInfoWithElderName>({} as ContractInfoWithElderName) // 详情数据

/** 日期数组格式化器：将 [year, month, day] 格式转换为 "YYYY-MM-DD" */
const formatDateArray = (dateArray: any): string => {
  if (Array.isArray(dateArray) && dateArray.length === 3) {
    const [year, month, day] = dateArray
    return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
  }
  return dateArray || '无'
}

/** 打开弹窗 */
const open = (data: ContractInfoWithElderName) => {
  dialogVisible.value = true
  detailData.value = data
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  width: 120px;
  color: #606266;
}

:deep(.el-descriptions__content) {
  padding: 12px 16px;
}
</style>
