<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="850px" :scroll="true" :max-height="340">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
      class="contract-info-form"
    >
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="老人" prop="elderId">
            <el-select
              v-model="formData.elderId"
              placeholder="请选择老人"
              clearable
              filterable
              class="!w-full"
            >
              <el-option
                v-for="item in elderProfiles"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签约日期" prop="contractDate">
            <el-date-picker
              v-model="formData.contractDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择签约日期"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="签约类型" prop="contractType">
            <el-select v-model="formData.contractType" placeholder="请选择签约类型" class="!w-full">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTRACT_INFO_CONTRACT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签约员工" prop="employeeIds">
            <el-select
              v-model="formData.employeeIds"
              placeholder="请选择签约员工"
              multiple
              collapse-tags
              collapse-tags-tooltip
              class="!w-full"
            >
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="user.nickname"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

      </el-row>

<!--      <el-row :gutter="16">-->
<!--       -->
<!--      </el-row>-->

      <!-- 签约期间 -->
      <el-divider content-position="left">签约期间</el-divider>
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="formData.startDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择开始日期"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker
              v-model="formData.endDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择结束日期"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="签约期限(月)" prop="contractPeriod">
            <el-input
              v-model="formData.contractPeriod"
              placeholder="选择开始和结束日期后自动计算"
              readonly
              class="!w-full"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 签约状态 -->
      <el-divider content-position="left">签约状态</el-divider>
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="签约状态" prop="contractStatus">
            <el-radio-group v-model="formData.contractStatus">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTRACT_INFO_CONTRACT_STATUS)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 其他信息 -->
      <el-divider content-position="left">其他信息</el-divider>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="签约原因" prop="contractReason">
            <el-input v-model="formData.contractReason" placeholder="请输入签约原因" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终止原因" prop="terminationReason">
            <el-input v-model="formData.terminationReason" placeholder="请输入终止原因" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ContractInfoApi, ContractInfoVO } from '@/api/elderArchives/contractinfo'
import { ArchivesProfileApi, ArchivesProfileSimpleVO } from '@/api/elderArchives/archivesProfile'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import dayjs from 'dayjs'

/** 老人签约信息 表单 */
defineOptions({ name: 'ContractInfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  elderId: undefined,
  employeeIds: undefined,
  contractDate: undefined,
  contractType: undefined,
  contractReason: undefined,
  contractPeriod: undefined,
  startDate: undefined,
  endDate: undefined,
  contractStatus: undefined,
  terminationReason: undefined,
  remark: undefined,
})
const formRules = reactive({
  elderId: [{ required: true, message: '请选择老人', trigger: 'change' }],
  contractDate: [{ required: true, message: '签约日期不能为空', trigger: 'blur' }],
  contractType: [{ required: true, message: '签约类型不能为空', trigger: 'change' }],
  contractPeriod: [{ required: true, message: '签约期限(月)不能为空', trigger: 'blur' }],
  startDate: [{ required: true, message: '开始日期不能为空', trigger: 'blur' }],
  endDate: [{ required: true, message: '结束日期不能为空', trigger: 'blur' }],
  contractStatus: [{ required: true, message: '签约状态不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
// 老人列表
const elderProfiles = ref<ArchivesProfileSimpleVO[]>([])
// 员工列表
const userList = ref<UserVO[]>([])

/** 获取老人列表 */
const getElderProfiles = async () => {
  try {
    elderProfiles.value = await ArchivesProfileApi.getArchivesProfileSimpleList()
  } catch (error) {
    console.error('获取老人列表失败', error)
  }
}

/** 获取员工列表 */
const getUserList = async () => {
  try {
    userList.value = await getSimpleUserList()
  } catch (error) {
    console.error('获取员工列表失败', error)
  }
}

/** 计算签约期限(月) */
const calculateContractPeriod = () => {
  if (formData.value.startDate && formData.value.endDate) {
    const startDate = dayjs(formData.value.startDate)
    const endDate = dayjs(formData.value.endDate)

    // 将结束日期加1天，这样计算的是包含结束日期的期间
    // 例如：2025-06-11 到 2026-06-10，实际计算 2025-06-11 到 2026-06-11
    const adjustedEndDate = endDate.add(1, 'day')

    // 使用 dayjs 的 diff 方法计算月份差
    const monthsDiff = adjustedEndDate.diff(startDate, 'month')

    // 确保期限不为负数
    if (monthsDiff >= 0) {
      formData.value.contractPeriod = monthsDiff
    } else {
      formData.value.contractPeriod = 0
    }
  } else {
    // 如果开始日期或结束日期为空，清空签约期限
    formData.value.contractPeriod = undefined
  }
}

/** 监听开始日期变化 */
watch(() => formData.value.startDate, () => {
  calculateContractPeriod()
})

/** 监听结束日期变化 */
watch(() => formData.value.endDate, () => {
  calculateContractPeriod()
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 获取老人列表和员工列表
  formLoading.value = true
  try {
    await getElderProfiles()
    await getUserList()

    // 修改时，设置数据
    if (id) {
      const data = await ContractInfoApi.getContractInfo(id)
      formData.value = { ...data }

      // 将employeeIds字符串转换为数组格式，以支持多选框显示
      if (formData.value.employeeIds && typeof formData.value.employeeIds === 'string') {
        formData.value.employeeIds = formData.value.employeeIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
      } else {
        formData.value.employeeIds = []
      }

      // 处理日期字段格式转换：LocalDate数组格式 [year, month, day] 转换为字符串格式 "YYYY-MM-DD"
      const convertDateArrayToString = (dateArray: any) => {
        if (Array.isArray(dateArray) && dateArray.length === 3) {
          const [year, month, day] = dateArray
          return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        }
        return dateArray
      }

      formData.value.contractDate = convertDateArrayToString(formData.value.contractDate)
      formData.value.startDate = convertDateArrayToString(formData.value.startDate)
      formData.value.endDate = convertDateArrayToString(formData.value.endDate)

      // 在编辑模式下，如果开始日期和结束日期都存在，重新计算签约期限
      if (formData.value.startDate && formData.value.endDate) {
        calculateContractPeriod()
      }
    }
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value } as unknown as ContractInfoVO

    // 将employeeIds数组转换为字符串格式
    if (Array.isArray(data.employeeIds)) {
      data.employeeIds = data.employeeIds.join(',')
    }

    if (formType.value === 'create') {
      await ContractInfoApi.createContractInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await ContractInfoApi.updateContractInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    elderId: undefined,
    employeeIds: [], // 重置为空数组，支持多选框
    contractDate: undefined,
    contractType: undefined,
    contractReason: undefined,
    contractPeriod: undefined,
    startDate: undefined,
    endDate: undefined,
    contractStatus: undefined,
    terminationReason: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.contract-info-form {
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  padding: 0 5px;
}

.contract-info-form :deep(.el-row) {
  margin-bottom: 3px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.contract-info-form :deep(.el-col) {
  box-sizing: border-box;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.contract-info-form :deep(.el-divider) {
  margin: 12px 0 8px 0;
  width: 100%;
}

.contract-info-form :deep(.el-divider__text) {
  font-weight: 600;
  color: #409eff;
  font-size: 13px;
}

.contract-info-form :deep(.el-form-item) {
  margin-bottom: 12px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.contract-info-form :deep(.el-form-item__label) {
  line-height: 32px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.contract-info-form :deep(.el-form-item__content) {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.contract-info-form :deep(.el-input) {
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
}

.contract-info-form :deep(.el-input__wrapper) {
  min-height: 32px;
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
}

.contract-info-form :deep(.el-select) {
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
}

.contract-info-form :deep(.el-select .el-input__wrapper) {
  min-height: 32px;
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
}

.contract-info-form :deep(.el-date-editor) {
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
}

.contract-info-form :deep(.el-radio-group) {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* 确保所有表单控件不会超出容器 */
.contract-info-form :deep(*) {
  box-sizing: border-box;
}
</style>
