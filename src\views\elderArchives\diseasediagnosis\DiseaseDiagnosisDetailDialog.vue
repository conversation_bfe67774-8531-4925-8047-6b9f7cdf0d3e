<template>
  <Dialog v-model="dialogVisible" title="疾病信息录入" width="900px">
    <div v-loading="loading">
      <!-- 疾病诊断详情列表 -->
      <div class="mb-4">
        <div class="flex justify-between items-center mb-2">
          <h3 class="text-lg font-bold">疾病诊断详情列表</h3>
          <el-button type="primary" @click="showAddForm = true" v-if="!showAddForm">
            <Icon icon="ep:plus" class="mr-5px" /> 新增疾病
          </el-button>
        </div>

        <el-table :data="detailList" border stripe>
          <el-table-column label="疾病编码(ICD-10)" prop="diseaseCode" min-width="120" />
          <el-table-column label="疾病名称" prop="diseaseName" min-width="150" />
          <el-table-column label="诊断描述" prop="description" min-width="200" show-overflow-tooltip />
          <el-table-column label="是否主要诊断" prop="isMain" min-width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.isMain ? 'danger' : 'info'">
                {{ scope.row.isMain ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="detailList.length === 0 && !loading" class="text-center py-4 text-gray-500">
          暂无疾病信息，请点击"新增疾病"按钮添加
        </div>
      </div>

      <!-- 新增/编辑表单 -->
      <div v-if="showAddForm" class="border p-4 rounded-md bg-gray-50">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-bold">{{ isEdit ? '编辑疾病信息' : '新增疾病信息' }}</h3>
          <el-button type="default" @click="cancelForm">取消</el-button>
        </div>

        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="140px"
        >
          <el-form-item label="疾病编码与名称" prop="diseaseCode">
            <el-select
              v-model="formData.diseaseCode"
              filterable
              placeholder="请选择疾病编码(ICD-10)"
              class="!w-full"
              @change="handleDiseaseCodeChange"
            >
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.ICD10_COMMON_DISEASE)"
                :key="dict.value"
                :label="`${dict.value} - ${dict.label}`"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="诊断描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入诊断描述"
            />
          </el-form-item>
          <el-form-item label="是否主要诊断" prop="isMain">
            <el-radio-group v-model="formData.isMain">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">保存</el-button>
            <el-button @click="cancelForm">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { DiseaseDiagnosisDetailApi, DiseaseDiagnosisDetailVO } from '@/api/elderArchives/diseasediagnosisdetail'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

/** 疾病信息录入对话框 */
defineOptions({ name: 'DiseaseDiagnosisDetailDialog' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const dialogVisible = ref(false) // 弹窗的是否展示
const loading = ref(false) // 加载中
const detailList = ref<DiseaseDiagnosisDetailVO[]>([]) // 疾病诊断详情列表
const showAddForm = ref(false) // 是否显示新增表单
const isEdit = ref(false) // 是否为编辑模式
const diagnosisId = ref<number>() // 当前疾病诊断ID
const formRef = ref() // 表单引用

// 表单数据
const formData = ref<DiseaseDiagnosisDetailVO>({
  id: undefined,
  diagnosisId: undefined,
  diseaseCode: '',
  diseaseName: '',
  description: '',
  isMain: false
})

// 表单验证规则
const formRules = reactive({
  diseaseCode: [{ required: true, message: '疾病编码不能为空', trigger: 'change' }],
  isMain: [{ required: true, message: '是否主要诊断不能为空', trigger: 'change' }]
})

/** 处理疾病编码变更 */
const handleDiseaseCodeChange = (value: string) => {
  if (!value) return

  // 从字典中获取对应的疾病名称
  const dictList = getDictOptions(DICT_TYPE.ICD10_COMMON_DISEASE)
  const selectedDict = dictList.find(dict => dict.value === value)

  if (selectedDict) {
    formData.value.diseaseName = selectedDict.label
  }
}

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  diagnosisId.value = id
  showAddForm.value = false
  isEdit.value = false
  resetForm()
  await loadDetailList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 加载疾病诊断详情列表 */
const loadDetailList = async () => {
  if (!diagnosisId.value) return

  loading.value = true
  try {
    const res = await DiseaseDiagnosisDetailApi.getDiseaseDiagnosisDetailPage({
      diagnosisId: diagnosisId.value,
      pageSize: 100 // 设置较大的页面大小以获取所有记录
    })
    detailList.value = res.list
  } catch (error) {
    console.error('获取疾病诊断详情列表失败', error)
    message.error('获取疾病诊断详情列表失败')
  } finally {
    loading.value = false
  }
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 设置疾病诊断ID
  formData.value.diagnosisId = diagnosisId.value

  loading.value = true
  try {
    if (isEdit.value && formData.value.id) {
      // 编辑模式
      await DiseaseDiagnosisDetailApi.updateDiseaseDiagnosisDetail(formData.value)
      message.success('修改成功')
    } else {
      // 新增模式
      await DiseaseDiagnosisDetailApi.createDiseaseDiagnosisDetail(formData.value)
      message.success('新增成功')
    }

    // 重新加载列表
    await loadDetailList()
    // 重置表单
    resetForm()
    // 隐藏表单
    showAddForm.value = false
  } catch (error) {
    console.error('保存疾病诊断详情失败', error)
    message.error('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 取消表单 */
const cancelForm = () => {
  showAddForm.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    diagnosisId: diagnosisId.value,
    diseaseCode: '',
    diseaseName: '',
    description: '',
    isMain: false
  }
  // 延迟一下再重置表单，确保值已经被清空
  setTimeout(() => {
    formRef.value?.resetFields()
  }, 0)
}

/** 编辑疾病诊断详情 */
const handleEdit = (row: DiseaseDiagnosisDetailVO) => {
  isEdit.value = true
  showAddForm.value = true

  // 复制行数据到表单
  formData.value = { ...row }

  // 确保疾病编码和疾病名称正确显示
  // 如果编码不存在于字典中，可能需要手动设置
  const dictList = getDictOptions(DICT_TYPE.ICD10_COMMON_DISEASE)
  const codeExists = dictList.some(dict => dict.value === row.diseaseCode)

  if (!codeExists && row.diseaseCode && row.diseaseName) {
    // 如果编码不在字典中但有值，保留原始值
    console.log('疾病编码不在字典中，保留原始值', row.diseaseCode, row.diseaseName)
  }
}

/** 删除疾病诊断详情 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DiseaseDiagnosisDetailApi.deleteDiseaseDiagnosisDetail(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await loadDetailList()
  } catch {}
}
</script>
