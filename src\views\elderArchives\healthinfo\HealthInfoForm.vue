<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="950px" :scroll="true" :max-height="470">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
      class="health-info-form"
    >
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="老人" prop="elderId">
            <el-select
              v-model="formData.elderId"
              placeholder="请选择老人"
              clearable
              filterable
              class="!w-full"
            >
              <el-option
                v-for="item in elderProfiles"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="评估日期" prop="assessmentDate">
            <el-date-picker
              v-model="formData.assessmentDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择评估日期"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="评估人员" prop="assessorName">
            <el-select
              v-model="formData.assessorName"
              placeholder="请选择评估人员"
              clearable
              filterable
              class="!w-full"
            >
              <el-option
                v-for="user in systemUsers"
                :key="user.id"
                :label="user.nickname"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="疼痛感" prop="painAssessment">
            <el-select v-model="formData.painAssessment" placeholder="请选择疼痛感" class="!w-full">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_PAIN_ASSESSMENT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 身体功能评估 -->
      <el-divider content-position="left">身体功能评估</el-divider>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="压力性损伤评估" prop="pressureInjury">
            <el-select v-model="formData.pressureInjury" placeholder="请选择压力性损伤评估" class="!w-full">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_PRESSURE_INJURY)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关节活动度评估" prop="jointActivity">
            <el-select v-model="formData.jointActivity" placeholder="请选择关节活动度评估" class="!w-full">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_JOINT_ACTIVITY)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="吞咽困难情况和症状" prop="swallowingDifficulty">
            <el-select v-model="formData.swallowingDifficulty" placeholder="请选择吞咽困难情况和症状" class="!w-full">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_SWALLOWING_DIFFICULTY)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="伤口情况" prop="woundCondition">
            <el-select
                v-model="formData.woundCondition"
                placeholder="请选择伤口情况"
                multiple
                collapse-tags
                collapse-tags-tooltip
                class="!w-full"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_WOUND_CONDITION)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 护理情况 -->
      <el-divider content-position="left">护理情况</el-divider>
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="特殊护理情况" prop="specialCare">
            <el-select
                v-model="formData.specialCare"
                placeholder="请选择特殊护理情况"
                multiple
                collapse-tags
                collapse-tags-tooltip
                class="!w-full"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_SPECIAL_CARE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 口腔健康 -->
      <el-divider content-position="left">口腔健康</el-divider>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="牙齿缺失情况" prop="teethMissing">
            <el-select
                v-model="formData.teethMissing"
                placeholder="请选择牙齿缺失情况"
                multiple
                collapse-tags
                collapse-tags-tooltip
                class="!w-full"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_TEETH_MISSING)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="义齿佩戴情况" prop="dentureWearing">
            <el-select
                v-model="formData.dentureWearing"
                placeholder="请选择义齿佩戴情况"
                multiple
                collapse-tags
                collapse-tags-tooltip
                class="!w-full"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_NTURE_WEARING)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 健康状况 -->
      <el-divider content-position="left">健康状况</el-divider>
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="营养状况异常" prop="nutritionStatus">
            <el-radio-group v-model="formData.nutritionStatus">
              <el-radio :label="true">有</el-radio>
              <el-radio :label="false">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="呼吸道功能异常" prop="respiratoryFunction">
            <el-radio-group v-model="formData.respiratoryFunction">
              <el-radio :label="true">有</el-radio>
              <el-radio :label="false">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="意识状态异常" prop="consciousnessState">
            <el-radio-group v-model="formData.consciousnessState">
              <el-radio :label="true">有</el-radio>
              <el-radio :label="false">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 其他信息 -->
      <el-divider content-position="left">其他信息</el-divider>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="其他特殊情况" prop="otherConditions">
            <el-input v-model="formData.otherConditions" placeholder="请输入其他特殊情况" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { HealthInfoApi, HealthInfoVO } from '@/api/elderArchives/healthinfo'
import { ArchivesProfileApi, ArchivesProfileSimpleVO } from '@/api/elderArchives/archivesProfile'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'

// 表单专用的数据类型接口，扩展自HealthInfoVO但支持多选字段的数组格式
interface HealthInfoFormData extends Partial<Omit<HealthInfoVO, 'woundCondition' | 'specialCare' | 'teethMissing' | 'dentureWearing'>> {
  woundCondition?: string | string[] // 支持字符串和数组两种格式
  specialCare?: string | string[] // 支持字符串和数组两种格式
  teethMissing?: string | string[] // 支持字符串和数组两种格式
  dentureWearing?: string | string[] // 支持字符串和数组两种格式
}

/** 老人健康信息 表单 */
defineOptions({ name: 'HealthInfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<HealthInfoFormData>({
  id: undefined,
  elderId: undefined,
  pressureInjury: undefined,
  jointActivity: undefined,
  woundCondition: [],
  specialCare: [],
  painAssessment: undefined,
  teethMissing: [],
  dentureWearing: [],
  swallowingDifficulty: undefined,
  nutritionStatus: undefined,
  respiratoryFunction: undefined,
  consciousnessState: undefined,
  otherConditions: undefined,
  assessmentDate: undefined,
  assessorName: undefined,
  remark: undefined,
})
const formRules = reactive({
  elderId: [{ required: true, message: '请选择老人', trigger: 'change' }],
  assessmentDate: [{ required: true, message: '评估日期不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
// 老人列表
const elderProfiles = ref<ArchivesProfileSimpleVO[]>([])
// 系统用户列表
const systemUsers = ref<UserVO[]>([])
// 用户store
const userStore = useUserStore()

// 根据用户ID获取用户昵称
const getUserNickname = (userId: number) => {
  const user = systemUsers.value.find(u => u.id === userId)
  return user ? user.nickname : userId?.toString() || ''
}

/** 获取老人列表 */
const getElderProfiles = async () => {
  try {
    elderProfiles.value = await ArchivesProfileApi.getArchivesProfileSimpleList()
  } catch (error) {
    console.error('获取老人列表失败', error)
  }
}

/** 获取系统用户列表 */
const getSystemUsers = async () => {
  try {
    systemUsers.value = await getSimpleUserList()
  } catch (error) {
    console.error('获取系统用户列表失败', error)
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 获取老人列表和系统用户列表
  formLoading.value = true
  try {
    await getElderProfiles()
    await getSystemUsers()

    // 新增时，自动填入当前用户作为评估人员
    if (type === 'create') {
      formData.value.assessorName = userStore.getUser.id
    }

    // 修改时，设置数据
    if (id) {
      const data = await HealthInfoApi.getHealthInfo(id)
      formData.value = { ...data }

      // 确保assessorName是数字类型，以便与下拉框选项匹配
      if (formData.value.assessorName && typeof formData.value.assessorName === 'string') {
        formData.value.assessorName = parseInt(formData.value.assessorName)
      }

      // 将字符串格式的多选字段转换为数组格式，以支持多选框显示
      const multiSelectFields = ['woundCondition', 'specialCare', 'teethMissing', 'dentureWearing']
      multiSelectFields.forEach(field => {
        const fieldValue = formData.value[field as keyof HealthInfoFormData]
        if (fieldValue && typeof fieldValue === 'string') {
          ;(formData.value as any)[field] = fieldValue.split(',').filter(item => item.trim() !== '')
        } else if (!Array.isArray(fieldValue)) {
          ;(formData.value as any)[field] = []
        }
      })

      // 处理评估日期：转换为el-date-picker需要的YYYY-MM-DD字符串格式
      if (formData.value.assessmentDate) {
        if (Array.isArray(formData.value.assessmentDate)) {
          // 数组格式 [year, month, day] 转换为 YYYY-MM-DD 字符串
          const [year, month, day] = formData.value.assessmentDate
          formData.value.assessmentDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        } else if (typeof formData.value.assessmentDate === 'string') {
          // 如果已经是字符串格式，保持不变
          // formData.value.assessmentDate = formData.value.assessmentDate
        } else {
          // 其他格式尝试转换为字符串
          const dateObj = new Date(formData.value.assessmentDate)
          if (!isNaN(dateObj.getTime())) {
            formData.value.assessmentDate = dateObj.toISOString().split('T')[0]
          }
        }
      }
    }
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    // 深拷贝表单数据，避免修改原始数据
    const data = JSON.parse(JSON.stringify(formData.value))

    // 将数组格式的多选字段转换为字符串格式，以匹配后端数据结构
    const multiSelectFields = ['woundCondition', 'specialCare', 'teethMissing', 'dentureWearing']
    multiSelectFields.forEach(field => {
      const fieldValue = data[field]
      if (Array.isArray(fieldValue)) {
        data[field] = fieldValue.join(',')
      } else if (!fieldValue) {
        data[field] = ''
      }
    })

    // 新增时移除id字段
    if (formType.value === 'create') {
      delete data.id
      await HealthInfoApi.createHealthInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await HealthInfoApi.updateHealthInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    elderId: undefined,
    pressureInjury: undefined,
    jointActivity: undefined,
    woundCondition: [],
    specialCare: [],
    painAssessment: undefined,
    teethMissing: [],
    dentureWearing: [],
    swallowingDifficulty: undefined,
    nutritionStatus: undefined,
    respiratoryFunction: undefined,
    consciousnessState: undefined,
    otherConditions: undefined,
    assessmentDate: undefined,
    assessorName: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.health-info-form {
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  padding: 0 5px;
}

.health-info-form :deep(.el-row) {
  margin-bottom: 3px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.health-info-form :deep(.el-col) {
  box-sizing: border-box;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.health-info-form :deep(.el-divider) {
  margin: 12px 0 8px 0;
  width: 100%;
}

.health-info-form :deep(.el-divider__text) {
  font-weight: 600;
  color: #409eff;
  font-size: 13px;
}

.health-info-form :deep(.el-form-item) {
  margin-bottom: 12px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.health-info-form :deep(.el-form-item__label) {
  line-height: 32px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.health-info-form :deep(.el-form-item__content) {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.health-info-form :deep(.el-input) {
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
}

.health-info-form :deep(.el-input__wrapper) {
  min-height: 32px;
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
}

.health-info-form :deep(.el-select) {
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
}

.health-info-form :deep(.el-select .el-input__wrapper) {
  min-height: 32px;
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
}

.health-info-form :deep(.el-date-editor) {
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
}

.health-info-form :deep(.el-radio-group) {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* 确保所有表单控件不会超出容器 */
.health-info-form :deep(*) {
  box-sizing: border-box;
}
</style>
