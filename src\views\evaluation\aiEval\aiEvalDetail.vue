<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick, defineProps, defineExpose, onBeforeUnmount } from 'vue'
import { ElMessage, ElDialog, ElMessageBox } from 'element-plus'
import { marked } from 'marked'
import { ArrowDown, InfoFilled } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { ResultApi } from '@/api/evaluation/result'
import formCreate from '@form-create/element-ui'
import { AIAnalysisUpdateReqVO, ResultVO } from '@/api/evaluation/result'
import { ArchivesProfileApi, ArchivesProfileVO } from '@/api/elderArchives/archivesProfile/index'
import download from '@/utils/download' // 确保导入下载工具
import { useMessage } from '@/hooks/web/useMessage' // 确保导入消息提示
import { timestampToDate } from '@/utils/formatTime'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TemplateApi } from '@/api/evaluation/template'
import { AiEvaluationApi, AiEvalAnalysisReqVO } from '@/api/ai/evaluation'

defineOptions({ name: 'AiEvalDetail' })

// 定义props接收组件传值
const props = defineProps({
  resultId: {
    type: [Number, String],
    default: null
  },
  isInListDetail: {
    type: Boolean,
    default: false
  }
})

// 响应式状态
const loading = ref(false)

// 医生信息
const elderName = ref<string>('')
const templateName = ref<string>('')
const evaluationTime = ref<string>('')
const aiInputs = ref<string>('')
const evaluatorAnalysis = ref<string>('')
const aiAnalysis = ref<string>('')
const aiInputElderInfo = ref<string>('')

// 接收路由参数
const route = useRoute()
const router = useRouter()
// 优先使用组件传入的resultId，其次使用路由参数
const id = ref(props.resultId || route.query.id)
const message = useMessage() // 消息弹窗

// 评估记录状态
const evaluationRecord = ref(null)
const previewForm = ref({})
const previewApi = ref(null)
const templateRule = ref([])
const templateOption = ref({
  submitBtn: { show: false }, // 不显示提交按钮
  resetBtn: { show: false }, // 不显示重置按钮
  form: {
    labelPosition: 'right',
    labelWidth: '120px',
    size: 'default'
  }
})

// 模态框状态
const dialogVisible = ref(false)
const aiReasoning = ref('')
const aiResult = ref('')
const isGenerating = ref(false)
const isRequesting = ref(false)
const isAnalysisFinish = ref(false)
const finishDialogVisible = ref(false)
let abortController: AbortController | null = null

// 编辑状态
const isEditing = ref<boolean>(false)

// 老人信息
const elderInfo = ref<ArchivesProfileVO | null>(null) // 存储老人信息

// 添加一个临时变量用于存储生成过程中的内容
const tempReasoning = ref('')
const tempAnalysis = ref('')

// 添加状态跟踪变量，防止内容在区域间跳转
const contentPhase = ref<'reasoning' | 'analysis' | 'unknown'>('unknown')

// 添加任务ID变量
const taskId = ref('')

// 添加模板类型的响应式变量
const templateType = ref<number>(0)

// 添加评估结果的响应式变量
const assessmentResult = ref<{
  totalScore?: number
  result?: string
}>({})

// 添加对比弹窗的状态
const compareDialogVisible = ref(false)
const originalResult = ref<ResultVO | null>(null)

// 添加模板类型选项
const templateTypeOptions = ref<any[]>([])

// 添加 API 密钥
const apiKey = ref('')

// 判断是否是复评模式
const isReEvaluation = computed(() => {
  return route.query.isReEvaluation === 'true'
})

// 切换编辑状态
const toggleEdit = () => {
  isEditing.value = !isEditing.value
}

// 保存评估师分析
const saveEvaluation = async () => {
  // 如果是普通表单类型，不允许保存评估师分析
  if (templateType.value === 0) {
    ElMessage.warning('普通表单不支持评估师分析')
    return
  }

  try {
    // 调用 API 更新评估师分析
    const response = await ResultApi.updateEvaluatorAnalysis({
      id: evaluationRecord.value?.id,
      evaluatorAnalysis: evaluatorAnalysis.value
    })

    if (response) {
      ElMessage.success('评估师分析保存成功')
      isEditing.value = false // 退出编辑模式
    } else {
      ElMessage.error('保存失败，请重试')
    }
  } catch (error) {
    // console.error('保存评估师分析失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 保存 AI 分析
const saveAiAnalysis = async () => {
  // 如果不是 AI 辅助评估原因，不允许保存 AI 分析
  if (templateType.value !== 2) {
    ElMessage.warning('只有 AI 辅助评估表单支持 AI 分析')
    return
  }

  try {
    // 调用 API 更新 AI 分析
    const response = await ResultApi.updateAiAnalysis({
      id: evaluationRecord.value?.id,
      aiAnalysis: `[think]${aiReasoning.value}[/think]\n` + aiAnalysis.value
    } as AIAnalysisUpdateReqVO)

    if (response) {
      ElMessage.success('AI 分析保存成功')
    } else {
      ElMessage.error('保存失败，请重试')
    }
  } catch (error) {
    // console.error('保存 AI 分析失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 获取原评估记录
const getOriginalResult = async (showDialog: boolean = false) => {
  if (!evaluationRecord.value?.connectResultId) {
    message.error('未找到关联的原评估记录')
    return
  }

  try {
    // 通过 connectResultId 获取原评估记录
    const data = await ResultApi.getResult(evaluationRecord.value.connectResultId)
    originalResult.value = data

    // 只有在需要显示弹窗时才显示
    if (showDialog) {
      compareDialogVisible.value = true
    }
  } catch (error) {
    // console.error('获取原评估记录失败:', error)
    message.error('获取原评估记录失败')
  }
}

// 显示对比弹窗
const showCompareDialog = () => {
  getOriginalResult(true) // 传入 true 表示需要显示弹窗
}

// 添加一个响应式变量来存储解析后的结果数据
const resultData = ref<any>(null)

// 获取评估详情
const getEvaluationDetail = async (newResultId?: number | string) => {
  loading.value = true
  try {
    // 如果提供了新的resultId，则使用新的ID
    if (newResultId !== undefined) {
      id.value = newResultId
    }

    if (!id.value) {
      ElMessage.error('评估ID不能为空')
      return
    }

    const data = await ResultApi.getResult(parseInt(id.value as string))
    evaluationRecord.value = data

    // 获取模板类型
    if (data.result) {
      resultData.value = JSON.parse(data.result)
      const options = resultData.value.options
      templateType.value = options?.form?.templateType || 0

      // 获取评估结果
      if (resultData.value.assessmentTemplateRuleResult) {
        assessmentResult.value = resultData.value.assessmentTemplateRuleResult
      }

      // 如果数据库中有直接存储的分数和结果，优先使用
      if (data.totalScore !== undefined || data.assessmentResult) {
        assessmentResult.value = {
          totalScore: data.totalScore,
          result: data.assessmentResult
        }
      }
    }

    // 根据 elderId 获取老人信息
    if (evaluationRecord.value?.elderId) {
      const elderData = await ArchivesProfileApi.getArchivesProfile(evaluationRecord.value?.elderId)
      elderInfo.value = elderData // 存储老人信息
    }
    aiInputElderInfo.value = `老人信息：\n姓名：${elderInfo.value?.name}，身份证号：${elderInfo.value?.idNumber
      }，性别：${elderInfo.value?.gender === 1 ? '男' : '女'
      }，出生日期：${elderInfo.value?.birthDate.join('-')}`
    // console.log(aiInputElderInfo.value)
    // console.log(elderInfo.value)

    // 其他字段赋值
    elderName.value = evaluationRecord.value?.elderName
    evaluationTime.value = evaluationRecord.value?.evaluationTime
    templateName.value = evaluationRecord.value?.templateName
    aiInputs.value = evaluationRecord.value?.aiInputs
    evaluatorAnalysis.value = evaluationRecord.value?.evaluatorAnalysis
    aiAnalysis.value = evaluationRecord.value?.aiAnalysis
    aiResult.value = aiAnalysis.value

    // 获取模板的 API 密钥
    if (data.templateId) {
      try {
        const apiKeyRes = await TemplateApi.getTemplateApiKey(data.templateId)
        if (apiKeyRes) {
          apiKey.value = apiKeyRes
        }
      } catch (error) {
        // console.error('获取API密钥失败:', error)
        message.error('获取API密钥失败')
      }
    }

    // 设置表单值
    if (evaluationRecord.value?.result) {
      const resultData = JSON.parse(evaluationRecord.value.result)

      // 优先从模板API获取最新的模板规则，确保与当前模板匹配
      if (data.templateId) {
        try {
          console.log('从模板API获取模板规则，模板ID:', data.templateId)
          const templateData = await TemplateApi.getTemplate(data.templateId)
          if (templateData && templateData.formSchema) {
            const schema = JSON.parse(templateData.formSchema)
            templateRule.value = schema.rule || []

            // 合并模板选项，保持基础配置不变
            templateOption.value = {
              ...templateOption.value,
              ...schema.option,
              submitBtn: { show: false }, // 强制不显示提交按钮
              resetBtn: { show: false }, // 强制不显示重置按钮
              form: {
                ...templateOption.value.form,
                ...schema.option?.form
              }
            }

            console.log('成功从模板API获取规则，规则数量:', templateRule.value.length)
            console.log('合并后的模板选项:', templateOption.value)
          } else {
            // 如果模板API获取失败，使用评估结果中保存的规则作为备用
            templateRule.value = resultData.rules || []
            console.log('使用评估结果中的备用规则，规则数量:', templateRule.value.length)
          }
        } catch (error) {
          console.error('从模板API获取规则失败，使用评估结果中的备用规则:', error)
          templateRule.value = resultData.rules || []
        }
      } else {
        // 如果没有模板ID，使用评估结果中保存的规则
        templateRule.value = resultData.rules || []
        console.log('没有模板ID，使用评估结果中的规则，规则数量:', templateRule.value.length)
      }

      // 从评估结果中恢复表单值
      const formValues: any = {}

      // 首先检查是否有新格式数据结构中的 formData
      if (resultData.formData) {
        // 新数据结构直接使用 formData
        Object.assign(formValues, resultData.formData)
        console.log('使用新格式formData恢复表单值:', formValues)
      }
      // 再检查是否有 hierarchicalResults 或 fullResult，这是新的层次化结构
      else if (resultData.fullResult && resultData.fullResult.rawFormData) {
        // 从完整的 JSON 结构中提取 rawFormData
        Object.assign(formValues, resultData.fullResult.rawFormData)
        console.log('使用fullResult.rawFormData恢复表单值:', formValues)
      }
      // 最后尝试旧的数据结构
      else if (resultData.assessmentResults && Array.isArray(resultData.assessmentResults)) {
        try {
          resultData.assessmentResults.forEach((result: any) => {
            if (result.items && Array.isArray(result.items)) {
              result.items.forEach((item: any) => {
                const rule = item.props && item.props.field ? item.props.field : item.name
                formValues[rule] = item.value
              })
            }
          })
          console.log('使用旧格式assessmentResults恢复表单值:', formValues)
        } catch (error) {
          console.error('处理旧格式评估结果时出错:', error)
          ElMessage.error('处理旧格式评估结果时出错')
        }
      }

      // 设置表单值
      previewForm.value = formValues
      console.log('最终设置的表单值:', previewForm.value)

      // 使用nextTick确保DOM更新后再设置表单数据
      nextTick(() => {
        if (previewApi.value && Object.keys(formValues).length > 0) {
          try {
            console.log('通过API设置表单值:', formValues)
            previewApi.value.setValue(formValues)
          } catch (error) {
            console.error('通过API设置表单值失败:', error)
          }
        }
      })
    }

    // 如果是复评记录，预先获取原评估记录，但不显示弹窗
    if (data.evaluationReason === '因对评估结果有疑问进行的复评' && data.connectResultId) {
      await getOriginalResult(false) // 传入 false 表示不需要显示弹窗
    }
  } catch (error) {
    // console.error('获取评估详情失败:', error)
    ElMessage.error('获取评估详情失败')
  } finally {
    loading.value = false
  }
}

// 获取表单类型名称的计算属性
const getTemplateTypeName = computed(() => {
  const option = templateTypeOptions.value.find((item: any) => item.value === templateType.value)
  return option ? option.label : '未知类型'
})

// 监听props中的resultId变化
watch(
  () => props.resultId,
  (newResultId) => {
    if (newResultId) {
      getEvaluationDetail(newResultId)
    }
  }
)

// 在 onMounted 中初始化字典选项
onMounted(() => {
  templateTypeOptions.value = getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_TYPE)
  getEvaluationDetail()

  // 添加网络状态监听
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
})

onBeforeUnmount(() => {
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)

  // 清理AI分析相关资源
  resetAIAnalysisState()
})

// 设置外部方法以便父组件调用
defineExpose({
  getEvaluationDetail
})

const generateAiAnalysis = async () => {
  // 如果不是 AI 辅助评估原因，不允许生成 AI 分析
  if (templateType.value !== 2) {
    ElMessage.warning('只有 AI 辅助评估表单支持 AI 分析')
    return
  }

  isGenerating.value = true
  // aiReasoning.value = ''
  tempReasoning.value = ''
  tempAnalysis.value = ''
  aiResult.value = ''
  contentPhase.value = 'unknown' // 重置内容阶段状态

  // 如果有正在进行的请求，则中断
  if (isRequesting.value && abortController) {
    abortController.abort()
  }

  // 创建新的 AbortController 实例
  abortController = new AbortController()

  await showAIResult(aiInputs.value)
}

// 响应式状态
const aiLoading = ref(false)
const exportLoading = ref(false)

// 导出 Word 报告
const exportWord = async () => {
  if (!id.value) {
    ElMessage.error('评估记录ID不能为空')
    return
  }
  exportLoading.value = true
  try {
    // 导出的二次确认
    await message.exportConfirm('是否确认导出评估报告？')
    const fileName =
      elderName.value + '_' + templateName.value + '_' + evaluationTime.value + '_评估报告.docx'
    const data = await ResultApi.exportResultWord({
      id: Number(id.value)
    })
    download.word(data, fileName)
  } catch (error) {
  } finally {
    exportLoading.value = false
  }
}


// 获取模板ID的方法
const getTemplateId = (): number => {
  // 从评估结果中获取模板ID
  if (evaluationRecord.value?.templateId) {
    return evaluationRecord.value.templateId;
  }
  throw new Error('无法获取模板ID');
}

// 移除未使用的错误类型枚举

// 移除未使用的复杂错误分类函数

// 重试计数器
let retryCount = 0
const MAX_RETRY_COUNT = 2

// 网络状态监听
const isOnline = ref(navigator.onLine)
const networkErrorShown = ref(false)

// 监听网络状态变化
const handleOnline = () => {
  isOnline.value = true
  networkErrorShown.value = false
  console.log('网络连接已恢复')
  ElMessage.success('网络连接已恢复')
}

const handleOffline = () => {
  isOnline.value = false
  if (!networkErrorShown.value) {
    networkErrorShown.value = true
    console.log('网络连接已断开')
    ElMessage.error('网络连接已断开，请检查网络设置')
  }
}

// 检查网络连接状态
const checkNetworkStatus = (): boolean => {
  if (!isOnline.value) {
    ElMessage.error('当前网络不可用，请检查网络连接')
    return false
  }
  return true
}

// 简化连接状态跟踪
const connectionState = ref<'idle' | 'connecting' | 'connected' | 'disconnected'>('idle')

// 移除复杂的心跳检测和监控工具，保持简洁

// 显示 AI 思考过程和结果
const showAIResult = async (aiInputs: string, isRetry = false) => {
  // 检查网络连接状态
  if (!checkNetworkStatus()) {
    return
  }

  // 如果不是重试，重置重试计数器
  if (!isRetry) {
    retryCount = 0
  }

  // 如果已经在请求中且不是重试，直接返回
  if (isRequesting.value && !isRetry) {
    console.log('已有请求在进行中，忽略新请求')
    return
  }

  aiLoading.value = true
  isRequesting.value = true
  isAnalysisFinish.value = false
  tempReasoning.value = ''
  tempAnalysis.value = ''
  let currentContent = ''
  connectionState.value = 'connecting'

  try {
    // 获取模板ID
    const templateId = getTemplateId();

    // 构建请求数据
    const requestData: AiEvalAnalysisReqVO = {
      query: aiInputs,
      elderInfo: aiInputElderInfo.value,
      user: 'user123',
      conversationId: '',
      responseMode: 'streaming'
    }

    dialogVisible.value = true

    // 如果有正在进行的请求，先中断
    if (abortController && !isRetry) {
      abortController.abort()
    }

    // 创建新的 AbortController 实例
    abortController = new AbortController()

    // 调用新的API进行流式AI分析
    await AiEvaluationApi.generateTemplateAnalysisStream({
      data: requestData,
      templateId: templateId,
      ctrl: abortController,
      onMessage: (event) => {
        try {
          connectionState.value = 'connected'

          const data = JSON.parse(event.data)

          if (data.code === 0 && data.data) {
            const respData = data.data
            switch (respData.event) {

              case 'text_chunk':
                if (respData.answer) {
                  // 保存任务ID
                  if (respData.taskId && !taskId.value) {
                    taskId.value = respData.taskId
                  }
                  currentContent += respData.answer

                  // 根据 </think> 标签分割内容
                  if (currentContent.includes('</think>')) {
                    const parts = currentContent.split('</think>')
                    if (parts.length >= 2) {
                      // 提取 </think> 标签前的内容作为思考过程（包含<think>标签）
                      let reasoningContent = parts[0].trim()
                      // 移除开头的 <think> 标签（如果存在）
                      if (reasoningContent.startsWith('<think>')) {
                        reasoningContent = reasoningContent.substring(7).trim()
                      }
                      tempReasoning.value = reasoningContent

                      // 提取 </think> 标签后的内容作为分析结果
                      tempAnalysis.value = parts[1].trim()
                      contentPhase.value = 'analysis'
                    }
                  } else {
                    // 如果还没有 </think> 标签，将内容放入思考内容
                    let reasoningContent = currentContent.replace(/<think>/g, '').trim();
                    // 移除开头的 <think> 标签（如果存在）
                    if (reasoningContent.startsWith('<think>')) {
                      reasoningContent = reasoningContent.substring(7).trim()
                    }
                    tempReasoning.value = reasoningContent
                    tempAnalysis.value = ''
                    contentPhase.value = 'reasoning'
                  }

                  aiResult.value = currentContent
                }
                break
              case 'message_end':
                isAnalysisFinish.value = true
                connectionState.value = 'disconnected'

                // 在生成完成时处理内容，确保 think 标签完整性
                let finalReasoning = tempReasoning.value
                let finalAnalysis = tempAnalysis.value

                // 如果当前内容包含 <think> 但没有 </think>，需要修复
                if (currentContent.includes('<think>') && !currentContent.includes('</think>')) {
                  // 查找 <think> 标签的位置
                  const thinkStartIndex = currentContent.indexOf('<think>')
                  if (thinkStartIndex !== -1) {
                    // 提取 <think> 标签后的内容作为思考过程
                    const thinkContent = currentContent.substring(thinkStartIndex + 7).trim()

                    // 查找可能的分析内容分割点（通常是换行或特定标记）
                    const lines = thinkContent.split('\n')
                    let reasoningLines: string[] = []
                    let analysisLines: string[] = []
                    let foundAnalysisStart = false

                    for (let i = 0; i < lines.length; i++) {
                      const line = lines[i].trim()
                      // 如果找到明显的分析开始标记，切换到分析部分
                      if (!foundAnalysisStart && (
                        line.startsWith('## ') ||
                        line.startsWith('### ') ||
                        line.includes('分析结果') ||
                        line.includes('评估结果') ||
                        line.includes('综合评估') ||
                        (i > 0 && line.length > 20 && !line.includes('思考') && !line.includes('考虑'))
                      )) {
                        foundAnalysisStart = true
                      }

                      if (foundAnalysisStart) {
                        analysisLines.push(lines[i])
                      } else {
                        reasoningLines.push(lines[i])
                      }
                    }

                    // 如果没有找到明显的分析开始点，将最后几行作为分析内容
                    if (!foundAnalysisStart && lines.length > 3) {
                      const splitPoint = Math.max(1, Math.floor(lines.length * 0.7))
                      reasoningLines = lines.slice(0, splitPoint)
                      analysisLines = lines.slice(splitPoint)
                    }

                    finalReasoning = reasoningLines.join('\n').trim()
                    finalAnalysis = analysisLines.join('\n').trim()
                  }
                }
                // 如果完全没有 think 标签，但有内容，将内容作为分析结果
                else if (!currentContent.includes('<think>') && !currentContent.includes('</think>') && currentContent.trim()) {
                  finalReasoning = '思考过程未完整生成'
                  finalAnalysis = currentContent.trim()
                }

                // 更新实际显示的内容
                aiReasoning.value = finalReasoning
                aiAnalysis.value = finalAnalysis

                // 分析完成后自动折叠，使用自定义折叠逻辑
                isReasoningCollapsed.value = ['1']
                // 重置重试计数器
                retryCount = 0
                break
              case 'error':
                console.error('AI分析服务端错误:', respData.error)
                connectionState.value = 'disconnected'
                const serverError = {
                  message: respData.error || 'AI分析服务异常',
                  status: 500
                }
                handleAIAnalysisError(serverError, aiInputs)
                break
              default:
                // 对于未知事件，不做特殊处理
                break
            }
          } else if (data.code !== 0) {
            // 处理业务错误
            connectionState.value = 'disconnected'
            const businessError = {
              message: data.msg || 'AI分析请求失败',
              status: 400
            }
            handleAIAnalysisError(businessError, aiInputs)
          }
        } catch (error) {
          console.error('解析SSE消息失败:', error)
        }
      },
      onError: (error) => {
        console.error('AI分析流式请求错误:', error)
        connectionState.value = 'disconnected'
        // 只有在请求状态为true时才处理错误，避免重复处理
        if (isRequesting.value) {
          handleAIAnalysisError(error, aiInputs)
        }
      },
      onClose: async () => {
        console.log('AI分析流式连接关闭')

        connectionState.value = 'disconnected'

        // 重置请求状态
        isRequesting.value = false

        // 如果分析已完成，不需要额外处理
        if (isAnalysisFinish.value) {
          return
        }

        // 如果分析未完成就关闭了连接，需要判断是否是异常中断
        const wasAborted = abortController?.signal.aborted
        const hasContent = currentContent.length > 0

        // 如果是用户主动中断，不做任何处理
        if (wasAborted) {
          return
        }

        if (hasContent) {
          // 有部分内容，保存当前进度
          if (tempReasoning.value || tempAnalysis.value) {
            aiReasoning.value = tempReasoning.value
            aiAnalysis.value = tempAnalysis.value
          }

          // 延迟询问用户，避免与其他弹窗冲突
          setTimeout(async () => {
            try {
              await ElMessageBox.confirm(
                '连接意外中断，但已接收到部分分析内容。是否重新开始分析？',
                '连接中断',
                {
                  confirmButtonText: '重新分析',
                  cancelButtonText: '保留当前内容',
                  type: 'warning'
                }
              )
              // 用户选择重新分析
              const networkError = {
                message: '连接意外中断',
                type: 'NETWORK_ERROR'
              }
              handleAIAnalysisError(networkError, aiInputs)
            } catch {
              // 用户选择保留当前内容
              ElMessage.info('已保留当前分析内容')
            }
          }, 500)
        } else {
          // 没有接收到任何内容，延迟后自动重试
          setTimeout(() => {
            const networkError = {
              message: '连接意外中断',
              type: 'NETWORK_ERROR'
            }
            handleAIAnalysisError(networkError, aiInputs)
          }, 1000)
        }
      }
    })

  } catch (error) {
    console.error('AI分析请求异常:', error)
    connectionState.value = 'disconnected'
    handleAIAnalysisError(error, aiInputs)
  } finally {
    // 只在这里重置加载状态，不清除 abortController
    // abortController 将在 onClose 回调或重置状态时清除
    aiLoading.value = false
    isGenerating.value = false
  }
}

// 统一的AI分析错误处理函数
const handleAIAnalysisError = async (error: any, aiInputs: string) => {
  console.error('AI分析错误:', error)

  // 重置请求状态
  isRequesting.value = false
  connectionState.value = 'disconnected'

  // 判断错误类型和是否可重试
  let errorMessage = 'AI分析请求失败'
  let shouldRetry = false

  if (error.name === 'AbortError' || error.message?.includes('aborted') || error.message?.includes('abort')) {
    errorMessage = '请求被用户中断'
    shouldRetry = false
  } else if (error.message?.includes('Failed to fetch') ||
             error.message?.includes('Network request failed') ||
             error.message?.includes('ERR_NETWORK') ||
             error.type === 'NETWORK_ERROR' ||
             error.type === 'error') {
    errorMessage = '网络连接失败，请检查网络连接'
    shouldRetry = true
  } else if (error.message?.includes('timeout') || error.message?.includes('连接意外中断')) {
    errorMessage = '连接超时或意外中断，请稍后重试'
    shouldRetry = true
  } else if (error.status >= 500) {
    errorMessage = '服务器暂时不可用，请稍后重试'
    shouldRetry = true
  } else if (error.status === 401) {
    errorMessage = '身份验证失败，请重新登录'
    shouldRetry = false
  } else if (error.status === 403) {
    errorMessage = '没有权限执行此操作'
    shouldRetry = false
  } else if (error.message === '无法获取模板ID') {
    errorMessage = '获取模板信息失败，请刷新页面重试'
    shouldRetry = false
  }

  // 根据错误类型决定是否显示重试选项
  if (shouldRetry && retryCount < MAX_RETRY_COUNT) {
    try {
      const retryDelay = Math.min(2000 * Math.pow(2, retryCount), 10000) // 指数退避，最大10秒
      await ElMessageBox.confirm(
        `${errorMessage}\n\n是否重试？（第 ${retryCount + 1} 次，共 ${MAX_RETRY_COUNT + 1} 次）\n将在 ${retryDelay / 1000} 秒后重试`,
        'AI分析失败',
        {
          confirmButtonText: '重试',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      retryCount++
      ElMessage.info(`正在重试中，请稍候...（${retryDelay / 1000}秒后开始）`)

      // 使用指数退避策略延迟重试
      setTimeout(() => {
        // 确保在重试前重置状态
        isRequesting.value = false
        connectionState.value = 'idle'
        showAIResult(aiInputs, true)
      }, retryDelay)
      return
    } catch {
      // 用户选择取消重试，重置状态
      resetAIAnalysisState()
      ElMessage.info('已取消重试')
    }
  } else {
    // 显示错误消息
    if (retryCount >= MAX_RETRY_COUNT) {
      ElMessage.error(`${errorMessage}，已达到最大重试次数`)
    } else {
      ElMessage.error(errorMessage)
    }
    // 重置状态
    resetAIAnalysisState()
  }
}

// 重置AI分析状态
const resetAIAnalysisState = () => {
  isRequesting.value = false
  isGenerating.value = false
  isAnalysisFinish.value = false
  tempReasoning.value = ''
  tempAnalysis.value = ''
  // 不清空已有的分析结果，让用户决定是否保留
  // aiReasoning.value = ''
  // aiAnalysis.value = ''
  retryCount = 0
  connectionState.value = 'idle'
  if (abortController) {
    try {
      abortController.abort()
    } catch (error) {
      console.warn('中断请求时出错:', error)
    }
    abortController = null
  }
}

// 添加停止响应方法
const stopResponse = async () => {
  if (taskId.value) {
    try {
      // 调用后端API停止AI分析
      const response = await AiEvaluationApi.stopEvalAnalysis(taskId.value)

      if (response.code === 0) {
        ElMessage.success('成功停止响应')
      } else {
        ElMessage.error('停止响应失败')
      }
    } catch (error) {
      console.error('停止响应出错:', error)
      ElMessage.error('停止响应出错')
    }
  }
}

// 修改关闭对话框方法
const closeDialog = async () => {
  if (!isAnalysisFinish.value) {
    const confirmClose = await ElMessageBox.confirm('正在生成 AI 分析，是否要关闭？', '确认关闭', {
      confirmButtonText: '是',
      cancelButtonText: '否',
      type: 'warning'
    })
    if (confirmClose) {
      await stopResponse() // 调用停止响应接口
      if (abortController) {
        abortController.abort() // 中断请求
      }

      dialogVisible.value = false // 关闭模态框
      taskId.value = '' // 清空任务ID
    }
  } else {
    dialogVisible.value = false // 直接关闭模态框
    taskId.value = '' // 清空任务ID
  }
}


// 配置 marked 以允许 HTML 标签
marked.setOptions({
  headerIds: false,
  mangle: false,
  sanitize: false, // 允许 HTML 标签
  breaks: true
})

// 计算渲染后的 Markdown 内容
const renderedAiAnalysis = computed(() => {
  if (aiAnalysis.value.includes('[think]')) {
    return marked(aiAnalysis.value)
  } else {
    return `[think]${aiReasoning.value}[/think]\n` + marked(aiAnalysis.value)
  }
})

const renderedAiResult = computed(() => {
  return marked(aiAnalysis.value)
})

const renderedTempResult = computed(() => {
  return marked(tempAnalysis.value)
})

// 移除未使用的脱敏函数

// 移除未使用的脱敏计算属性

// 处理查看按钮点击事件
const handleViewClick = () => {
  if (aiAnalysis.value.includes('[think]')) {
    const parts = aiAnalysis.value.split('[/think]')
    if (parts.length === 2) {
      // 提取 think 标签中的内容
      aiReasoning.value = parts[0].replace('[think]', '').trim()
      // 提取 think 标签后的内容
      aiAnalysis.value = parts[1].trim()
    }
  }
  finishDialogVisible.value = true
  isReasoningCollapsed.value = ['1']
}

// 递归处理规则和收集结果
const processRules = (rules: any[]) => {
  const results: any[] = []

  const processRule = (rule: any) => {
    if (rule._fc_drag_tag === 'elCard') {
      // 如果是卡片，收集卡片标题和子项的值
      const cardResult = {
        title: rule.props.header,
        items: [] as any[],
        type: rule.type
      }

      // 处理卡片的子项
      if (rule.children && rule.children.length > 0) {
        rule.children.forEach((child: any) => {
          if (child._fc_drag_tag === 'elCard') {
            // 如果子项也是卡片，递归处理
            const nestedResults = processRule(child)
            if (nestedResults) {
              cardResult.items.push(nestedResults)
            }
          } else if (child._fc_drag_tag !== 'elAlert') {
            // 如果是普通组件且有值，则添加到结果中
            const value = child.value || previewForm.value[child.field]
            if (value !== undefined) {
              cardResult.items.push({
                type: child.type,
                name: child.name,
                info: child.info || '',
                title: child.title || '',
                props: child.props,
                options: child.options,
                value: value
              })
            }
          }

          // 如果子项有自己的子项，递归处理
          if (child.children && child.children.length > 0) {
            const childResults = []
            child.children.forEach((grandChild: any) => {
              const grandChildResult = processRule(grandChild)
              if (grandChildResult) {
                childResults.push(grandChildResult)
              }
            })
            if (childResults.length > 0) {
              cardResult.items.push({
                type: child.type,
                name: child.name,
                info: child.info || '',
                title: child.title || '',
                items: childResults
              })
            }
          }
        })
      }

      if (cardResult.items.length > 0) {
        return cardResult
      }
    } else if (rule._fc_drag_tag !== 'elAlert') {
      // 处理其他类型的组件
      const otherResult = {
        title: rule.title || rule.name,
        items: [] as any[],
        type: rule.type
      }

      // 处理子项
      if (rule.children && rule.children.length > 0) {
        rule.children.forEach((child: any) => {
          if (child._fc_drag_tag === 'elCard') {
            // 如果子项是卡片，递归处理
            const nestedResults = processRule(child)
            if (nestedResults) {
              otherResult.items.push(nestedResults)
            }
          } else {
            const value = child.value || previewForm.value[child.field]
            if (value !== undefined) {
              otherResult.items.push({
                type: child.type,
                name: child.name,
                info: child.info || '',
                title: child.title || '',
                props: child.props,
                options: child.options,
                value: value
              })
            }
          }

          // 递归处理子项的子项
          if (child.children && child.children.length > 0) {
            const childResults = []
            child.children.forEach((grandChild: any) => {
              const grandChildResult = processRule(grandChild)
              if (grandChildResult) {
                childResults.push(grandChildResult)
              }
            })
            if (childResults.length > 0) {
              otherResult.items.push({
                type: child.type,
                name: child.name,
                info: child.info || '',
                title: child.title || '',
                items: childResults
              })
            }
          }
        })
      } else {
        // 如果没有子项，直接处理当前组件
        const value = rule.value || previewForm.value[rule.field]
        if (value !== undefined) {
          otherResult.items.push({
            type: rule.type,
            name: rule.name,
            info: rule.info || '',
            title: rule.title || '',
            props: rule.props,
            options: rule.options,
            value: value
          })
        }
      }

      if (otherResult.items.length > 0) {
        return otherResult
      }
    }
    return null
  }

  // 处理所有规则
  rules.forEach((rule) => {
    const result = processRule(rule)
    if (result) {
      results.push(result)
    }
  })

  return results
}

// 保存复评结果
const handleSubmit = async () => {
  if (!evaluationRecord.value) {
    message.error('获取评估记录失败')
    return
  }

  try {
    const formData = previewForm.value
    const copyRules = formCreate.copyRules(templateRule.value)
    let resultData = {}

    try {
      resultData = JSON.parse(evaluationRecord.value.result)
    } catch (error) {
      // console.error('解析评估结果失败:', error)
      message.error('解析评估结果失败')
      return
    }

    // console.log('原始评估数据:', resultData)
    const templateOptions = resultData.options || []

    // 收集评估结果
    const assessmentResults = processRules(copyRules)

    // 构建层次化结果，兼容新格式
    const hierarchicalResults = resultData.hierarchicalResults || {}

    // 创建包含元数据和结构化结果的完整JSON对象
    const fullResultJSON = {
      metadata: {
        elderInfo: {
          id: evaluationRecord.value.elderId || 0,
          name: elderInfo.value?.name || '',
          gender: elderInfo.value?.gender,
          birthDate: elderInfo.value?.birthDate,
          age: elderInfo.value
            ? new Date().getFullYear() - new Date(elderInfo.value.birthDate).getFullYear()
            : ''
        },
        templateInfo: {
          id: evaluationRecord.value.templateId || 0,
          name: evaluationRecord.value.templateName,
          type: templateType.value
        },
        evaluationInfo: {
          evaluatorId: Number(route.query.evaluatorId) || 0,
          evaluatorName: route.query.evaluatorName as string,
          evaluationReason: '因对评估结果有疑问进行的复评',
          evaluationTime: new Date().getTime()
        }
      },
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiInput: aiInputs.value || '',
      hierarchicalResults: hierarchicalResults,
      rawFormData: formData
    }

    // 创建新的评估记录，状态为已提交(1)
    await ResultApi.createResult({
      elderId: evaluationRecord.value.elderId,
      elderName: evaluationRecord.value.elderName,
      templateId: evaluationRecord.value.templateId,
      templateName: evaluationRecord.value.templateName,
      evaluatorId: Number(route.query.evaluatorId),
      evaluatorName: route.query.evaluatorName as string,
      evaluationReason: '因对评估结果有疑问进行的复评',
      evaluationTime: new Date().getTime(),
      result: JSON.stringify({
        options: templateOptions,
        rules: copyRules,
        assessmentResults: assessmentResults,
        formData: formData,
        hierarchicalResults: hierarchicalResults,
        fullResult: fullResultJSON
      }),
      connectResultId: evaluationRecord.value.id, // 关联原评估记录
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiAnalysis: aiAnalysis.value || '',
      aiInputs: aiInputs.value || '',
      status: 1, // 已提交状态
      type: evaluationRecord.value.type || 0 // 保持与原评估相同的类型
    })

    message.success('保存复评结果成功')
    // 跳转回列表页，添加时间戳参数触发页面刷新
    router.replace({
      path: '/evaluation/result',
      query: { refresh: Date.now().toString() }
    })
  } catch (error) {
    // console.error('保存复评结果失败:', error)
    message.error('保存复评结果失败')
  }
}

// 监听弹窗关闭事件
const handleCompareDialogClose = () => {
  // 重置相关状态
  originalResult.value = null
  compareDialogVisible.value = false
}


// 计算评估有效期信息，用于显示
const validityInfo = computed(() => {
  if (!evaluationRecord.value) return { text: '暂无有效期信息', status: 'info', daysLeft: 0 }

  try {
    // 获取评估时间
    const evaluationTime = evaluationRecord.value.evaluationTime
    if (!evaluationTime) return { text: '未找到评估时间', status: 'warning', daysLeft: 0 }

    // 获取模板配置中的有效期设置
    let validityPeriod = 3 // 默认3
    let validityUnit = 'month' // 默认月
    let validityStartTimeType = 'evaluationTime' // 默认评估时间
    let validityStartTime = '' // 固定起始日期

    // 尝试从不同来源获取有效期设置
    if (evaluationRecord.value.result) {
      try {
        const resultData = JSON.parse(evaluationRecord.value.result)

        // 首先尝试从新格式(元数据)中获取
        if (resultData.fullResult?.metadata?.templateInfo) {
          const templateInfo = resultData.fullResult.metadata.templateInfo
          validityPeriod = templateInfo.validityPeriod || validityPeriod
          validityUnit = templateInfo.validityUnit || validityUnit
          validityStartTimeType = templateInfo.validityStartTimeType || validityStartTimeType
          validityStartTime = templateInfo.validityStartTime || validityStartTime
        }
        // 然后尝试从旧格式中获取
        else if (resultData.options?.form) {
          validityPeriod = resultData.options.form.validityPeriod || validityPeriod
          validityUnit = resultData.options.form.validityUnit || validityUnit
          validityStartTimeType =
            resultData.options.form.validityStartTimeType || validityStartTimeType
          validityStartTime = resultData.options.form.validityStartTime || validityStartTime
        }
      } catch (e) {
        // console.error('解析评估结果数据失败:', e)
        ElMessage.error('解析评估结果数据失败')
      }
    }

    // 确定起始日期
    const day = 24 * 60 * 60 * 1000 // 一天的毫秒数
    let startDate

    if (validityStartTimeType === 'fixedDate' && validityStartTime) {
      // 如果是固定日期模式且有设置日期，使用该固定日期作为起点
      startDate = new Date(validityStartTime)
    } else {
      // 默认使用评估时间作为起点
      startDate = new Date(evaluationTime)
    }

    // 根据单位计算到期日期
    let expiryDate = new Date(startDate)

    switch (validityUnit) {
      case 'day':
        expiryDate = new Date(startDate.getTime() + validityPeriod * day)
        break
      case 'week':
        expiryDate = new Date(startDate.getTime() + validityPeriod * 7 * day)
        break
      case 'month':
      default:
        expiryDate.setMonth(startDate.getMonth() + validityPeriod)
        break
    }

    // 计算剩余天数
    const today = new Date()
    const daysLeft = Math.ceil((expiryDate.getTime() - today.getTime()) / day)

    // 生成有效期文本
    let validityText = `${validityPeriod} `
    switch (validityUnit) {
      case 'day':
        validityText += validityPeriod > 1 ? '天' : '天'
        break
      case 'week':
        validityText += validityPeriod > 1 ? '周' : '周'
        break
      case 'month':
      default:
        validityText += validityPeriod > 1 ? '个月' : '个月'
        break
    }

    let startInfo = ''
    if (validityStartTimeType === 'fixedDate' && validityStartTime) {
      startInfo = `，从固定日期 ${validityStartTime} 起`
    } else {
      startInfo = '，从评估时间起'
    }

    // 根据有效期状态返回不同的显示信息
    if (daysLeft <= 0) {
      return {
        text: `评估已过期，有效期${validityText}${startInfo}，过期时间：${expiryDate.toLocaleDateString()}`,
        status: 'danger',
        daysLeft: 0
      }
    } else if (daysLeft <= 7) {
      return {
        text: `评估即将过期，剩余 ${daysLeft} 天，有效期${validityText}${startInfo}`,
        status: 'warning',
        daysLeft
      }
    } else {
      return {
        text: `评估有效期剩余 ${daysLeft} 天，有效期${validityText}${startInfo}`,
        status: 'success',
        daysLeft
      }
    }
  } catch (error) {
    // console.error('计算评估有效期失败:', error)
    ElMessage.error('计算有效期时出错')
    return { text: '计算有效期时出错', status: 'error', daysLeft: 0 }
  }
})

// 暂存相关变量
const savingDraft = ref(false)

// 暂存复评结果
const saveDraft = async () => {
  if (!isReEvaluation.value) return

  savingDraft.value = true
  try {
    // 获取表单数据
    const formData = previewApi.value ? previewApi.value.formData() : {}

    // 复制规则以避免修改原始数据
    const copyRules = formCreate.copyRules(templateRule.value)

    if (!resultData.value) {
      ElMessage.error('评估结果数据不存在')
      return
    }

    // 获取模板选项
    const templateOptions = resultData.value.options || []

    // 收集评估结果
    const assessmentResults = processRules(copyRules)

    // 构建层次化结果，兼容新格式
    const hierarchicalResults = resultData.value.hierarchicalResults || {}

    // 创建包含元数据和结构化结果的完整JSON对象
    const fullResultJSON = {
      metadata: {
        elderInfo: {
          id: evaluationRecord.value.elderId || 0,
          name: elderInfo.value?.name || '',
          gender: elderInfo.value?.gender,
          birthDate: elderInfo.value?.birthDate,
          age: elderInfo.value
            ? new Date().getFullYear() - new Date(elderInfo.value.birthDate).getFullYear()
            : ''
        },
        templateInfo: {
          id: evaluationRecord.value.templateId || 0,
          name: evaluationRecord.value.templateName,
          type: templateType.value
        },
        evaluationInfo: {
          evaluatorId: Number(route.query.evaluatorId) || 0,
          evaluatorName: route.query.evaluatorName as string,
          evaluationReason: '因对评估结果有疑问进行的复评',
          evaluationTime: new Date().getTime()
        }
      },
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiInput: aiInputs.value || '',
      hierarchicalResults: hierarchicalResults,
      rawFormData: formData
    }

    // 创建新的评估记录，状态为暂存(2)
    await ResultApi.createResult({
      elderId: evaluationRecord.value.elderId,
      elderName: evaluationRecord.value.elderName,
      templateId: evaluationRecord.value.templateId,
      templateName: evaluationRecord.value.templateName,
      evaluatorId: Number(route.query.evaluatorId),
      evaluatorName: route.query.evaluatorName as string,
      evaluationReason: '因对评估结果有疑问进行的复评',
      evaluationTime: new Date().getTime(),
      result: JSON.stringify({
        options: templateOptions,
        rules: copyRules,
        assessmentResults: assessmentResults,
        formData: formData,
        hierarchicalResults: hierarchicalResults,
        fullResult: fullResultJSON
      }),
      connectResultId: evaluationRecord.value.id, // 关联原评估记录
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiAnalysis: aiAnalysis.value || '',
      aiInputs: aiInputs.value || '',
      status: 2, // 暂存状态
      type: evaluationRecord.value.type || 0 // 保持与原评估相同的类型
    })

    message.success('暂存复评结果成功')
    // 跳转回列表页，添加时间戳参数触发页面刷新
    router.replace({
      path: '/evaluation/result',
      query: { refresh: Date.now().toString() }
    })
  } catch (error) {
    console.error('暂存复评结果失败:', error)
    message.error('暂存复评结果失败')
  } finally {
    savingDraft.value = false
  }
}

// 在script setup部分添加一个新的ref用于结果容器
const resultContainer = ref(null)

// 监听tempAnalysis的变化，确保内容更新时自动滚动到底部
watch(
  () => tempAnalysis.value,
  (newVal, oldVal) => {
    if (newVal && (!oldVal || newVal.length > oldVal.length)) {
      nextTick(() => {
        if (resultContainer.value) {
          // 自动滚动到底部，确保用户能看到最新内容
          resultContainer.value.scrollTop = resultContainer.value.scrollHeight
        }
      })
    }
  },
  { immediate: true }
)

// 添加对tempReasoning的监听
watch(
  () => tempReasoning.value,
  (newVal, oldVal) => {
    if (newVal && (!oldVal || newVal.length > oldVal.length)) {
      nextTick(() => {
        if (reasoningContainer.value) {
          // 自动滚动到底部，确保用户能看到最新思考内容
          reasoningContainer.value.scrollTop = reasoningContainer.value.scrollHeight
        }
      })
    }
  },
  { immediate: true }
)

// 在script部分添加reasoningContainer引用，如果还不存在
const reasoningContainer = ref(null)

// 在script部分添加一个新的变量控制折叠状态
const isReasoningCollapsed = ref<string[]>([]) // 默认展开分析过程
</script>

<template>
  <div class="flex flex-col h-full" v-loading="loading" element-loading-text="加载中...">
    <div class="flex flex-1">
      <div class="flex-1 mr-4 form-container">
        <!-- <el-card> -->
        <!-- <div>
            <el-tabs v-model="activeTab">
              <el-tab-pane label="单次评估" name="single" />
            </el-tabs>
          </div> -->
        <div class="space-y-4">
          <div class="border rounded-lg">
            <!-- <el-card class="mb-1">
                <div class="eval-info">
                  <div class="info-item">
                    <span class="label">评估师：</span>
                    <span class="value">{{ evaluationRecord?.evaluatorName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">评估原因：</span>
                    <span class="value">{{ evaluationRecord?.evaluationReason }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">评估时间：</span>
                    <span class="value">{{
                      timestampToDate(evaluationRecord?.evaluationTime)
                      }}</span>
                  </div>
                </div>
              </el-card> -->
            <el-card class="left-card">
              <div>
                <div class="">
                  <!-- <div class="flex justify-end space-x-3 border-t">
                      <el-button plain type="primary" @click="toggleDetails(record.id)">
                        修改评估师分析
                      </el-button>
                      <el-button type="primary" @click="exportReport(record.id)">
                        导出报告
                      </el-button>
                    </div> -->
                  <div class="flex justify-between items-center mb-4">
                    <h2 class="font-bold">评估结果</h2>
                    <div class="flex gap-2">
                      <el-button v-if="isReEvaluation" type="success" @click="saveDraft" :loading="savingDraft">
                        暂存
                      </el-button>
                      <el-button v-if="isReEvaluation" type="primary" @click="handleSubmit">
                        保存复评结果
                      </el-button>
                      <el-button
type="primary" v-if="!isReEvaluation && !isInListDetail" @click="exportWord"
                        :loading="exportLoading" :disabled="!id">
                        导出报告
                      </el-button>
                      <!-- 如果是复评记录，显示对比按钮 -->
                      <el-button
v-if="evaluationRecord?.evaluationReason === '因对评估结果有疑问进行的复评'" type="primary" plain
                        @click="showCompareDialog">
                        对比原评估结果
                      </el-button>
                    </div>
                  </div>

                  <!-- <div class="mb-2 font-bold text-lg">评估结果</div> -->
                  <div class="bg-gray-50 p-4 rounded-lg text-sm min-h-74vh form-content-container">
                    <div class="gap-4">
                      <h3>{{ templateName }}</h3>
                      <div class="text-gray-500 text-sm mt-2 mb-2">
                        <span class="mr-4">表单类型: {{ getTemplateTypeName }}</span>
                        <span>版本号: {{ resultData?.options?.version || '1.0.0' }}</span>
                      </div>

                      <!-- 有效期状态信息 -->
                      <div v-if="!isReEvaluation" class="mt-2 mb-4">
                        <el-tag v-if="validityInfo.status === 'danger'" type="danger" effect="dark" round>已过期</el-tag>
                        <el-tag v-else-if="validityInfo.status === 'warning'" type="warning" effect="dark" round>即将过期
                          (剩余 {{ validityInfo.daysLeft }} 天)</el-tag>
                        <el-tag v-else type="success" effect="dark" round>有效 ({{ validityInfo.daysLeft }} 天)</el-tag>
                      </div>

                      <el-divider />
                      <form-create
v-model="previewForm" v-model:api="previewApi" :rule="templateRule"
                        :option="templateOption" :disabled="!isReEvaluation" />
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
        <!-- <div class="space-y-4">

          </div> -->
        <!-- </el-card> -->
      </div>

      <!-- 老人信息 -->
      <el-card class="right-card w-110 info-container">
        <!-- 评估信息 -->
        <div class="eval-info">
          <div class="info-item">
            <span class="label">评估师：</span>
            <span class="value">{{ evaluationRecord?.evaluatorName }}</span>
          </div>
          <div class="info-item">
            <span class="label">评估原因：</span>
            <span class="value">{{ evaluationRecord?.evaluationReason }}</span>
          </div>
          <div class="info-item">
            <span class="label">评估时间：</span>
            <span class="value">{{ timestampToDate(evaluationRecord?.evaluationTime) }}</span>
          </div>
        </div>
        <div class="flex flex-col items-center">
          <!-- <div class="w-24 h-24 rounded-full overflow-hidden mb-4">
            <img
              src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
              class="w-full h-full object-cover"
              alt="老人头像"
            />
            </div> -->
          <!-- <h3 class="text-lg font-medium">{{ elderInfo.name }}</h3> -->
        </div>
        <div class="mt-1 space-y-4">
          <div class="flex justify-between">
            <span>老人姓名:</span>
            <span>{{ elderInfo?.name }}</span>
          </div>
          <div class="flex justify-between">
            <span>年龄:</span>
            <span>{{ elderInfo ? new Date().getFullYear() - elderInfo.birthDate[0] : '' }}岁</span>
          </div>
          <!--          <div class="flex justify-between">-->
          <!--            <span>身份证号:</span>-->
          <!--            <span>{{ maskedIdNumber }}</span>-->
          <!--          </div>-->
          <div class="flex justify-between">
            <span>性别:</span>
            <span>{{ elderInfo?.gender === 1 ? '男' : '女' }}</span>
          </div>
          <div class="flex justify-between">
            <span>出生日期:</span>
            <span>{{ elderInfo?.birthDate.join('-') }}</span>
          </div>
          <!--          <div class="flex justify-between">-->
          <!--            <span>联系电话:</span>-->
          <!--            <span>{{ maskedPhone }}</span>-->
          <!--          </div>-->
        </div>
        <el-divider />

        <!-- 有效期提示 -->
        <!-- <el-alert v-if="validityInfo.status === 'danger' || validityInfo.status === 'warning'"
          :type="validityInfo.status === 'danger' ? 'error' : 'warning'" show-icon :closable="false" class="mb-4">
          <template #title>
            <span class="font-bold">
              {{ validityInfo.status === 'danger' ? '评估结果已过期' :
                validityInfo.status === 'warning' ? '评估结果即将过期' : '评估结果有效' }}
            </span>
          </template>
<p>{{ validityInfo.text }}</p>
</el-alert> -->

        <div>
          <!-- 评估结果展示 - 仅在 templateType 为 1 或 2，且有评估结果时显示，且不在清单详情页面中 -->
          <div
v-if="
            !isInListDetail &&
            (templateType === 1 || templateType === 2) &&
            assessmentResult.totalScore !== undefined &&
            assessmentResult.result
          " class="mt-1 mb-4">
            <div class="text-lg font-bold mb-3">评估结果</div>
            <div class="bg-gray-50 p-4 rounded-lg text-sm">
              <div class="flex justify-between mb-2">
                <span>总分：</span>
                <span class="font-medium">{{ assessmentResult.totalScore }}</span>
              </div>
              <div class="flex justify-between">
                <span>评估等级：</span>
                <span class="font-medium">{{ assessmentResult.result }}</span>
              </div>
            </div>
          </div>

          <!-- 评估师分析 - 仅在 templateType 为 1 或 2 时显示，且不在清单详情页面中 -->
          <div v-if="!isInListDetail && (templateType === 1 || templateType === 2)" class="mt-1">
            <div class="flex justify-between mb-3">
              <div class="text-lg font-bold">评估师分析</div>
              <div class="flex justify-end">
                <el-button plain type="primary" @click="isEditing ? saveEvaluation() : toggleEdit()">
                  {{ isEditing ? '保存' : '修改' }}
                </el-button>
              </div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg text-sm">
              <div class="text-gray-700">
                <el-input
type="textarea" v-model="evaluatorAnalysis" :rows="3" :disabled="!isEditing"
                  placeholder="请输入评估师分析" v-if="isEditing" />
                <div v-else>{{ evaluatorAnalysis }}</div>
              </div>
            </div>
          </div>

          <!-- AI 分析 - 仅在 templateType 为 2 时显示，且不在清单详情页面中 -->
          <div v-if="!isInListDetail && templateType === 2" class="mt-4">
            <div class="mb-3">
              <div class="mb-2">
                <div class="text-lg font-bold">
                  AI 分析
                  <i class="text-gray-500 pl-1 pt-2 cursor-pointer" @click="generateAiAnalysis()">
                    <svg
t="1740238555153" class="icon" viewBox="0 0 1091 1024" version="1.1"
                      xmlns="http://www.w3.org/2000/svg" p-id="6063" width="16" height="16">
                      <path
                        d="M213.150633 94.760681c23.921591-25.821677 44.335332-67.818441 56.028166-94.760681 11.595394 27.03968 31.960415 69.182605 56.223047 94.858122 28.062803 29.670568 29.670568 77.31887 54.176801 107.038158 67.331239-29.719288 13.154439-78.975354 37.660672-107.038158 67.33124-24.262632 25.675516-44.627652 67.818441-56.223047 94.858122-11.692835-26.94224-32.106575-68.939005-56.028166-94.760681-26.455039-28.550005-72.641736-53.9332-99.681416-67.428681 27.03968-13.44676 73.226377-38.878675 99.681416-67.42868z m0 0M907.948615 532.023979c20.023979-21.583024 37.07603-56.710248 46.86878-79.267675 9.695309 22.606147 26.747359 57.879532 47.063659 79.365116 23.48311 24.798554 64.700352 45.309735 89.547626 56.320487-24.847274 10.962033-66.064516 31.473213-89.547626 56.320487-20.3163 21.485584-37.319631 56.710248-47.063659 79.365116-9.792749-22.557427-26.8448-57.684651-46.86878-79.267676-22.118946-23.872871-60.75402-45.114854-83.360167-56.369207 22.606147-11.254353 61.241222-32.496336 83.360167-56.369207z m0 0M675.602246 829.363022c11.887715-12.813398 22.070226-33.178418 28.306404-47.30726 6.187458 14.128842 16.418689 34.640023 28.452564 47.35598 8.672186 9.110667 20.949662 17.198211 32.447617 23.53183 7.600343 4.189932 15.103245 7.795223 21.339423 10.620992-6.236179 2.825768-13.739081 6.431059-21.339423 10.620991-11.497954 6.382339-23.824151 14.421163-32.447617 23.53183-12.033876 12.715958-22.265106 33.178418-28.452564 47.355981-6.236179-14.128842-16.467409-34.493862-28.306404-47.307261-8.136264-8.769626-19.585498-16.75973-30.30393-23.239509-7.161861-4.336093-14.177562-8.087544-19.926539-11.010752 5.748977-2.923209 12.764678-6.67466 19.926539-10.962033 10.669712-6.479779 22.167666-14.518603 30.30393-23.239509z m0 0"
                        p-id="6064" />
                      <path
                        d="M743.376837 53.274066m113.651687 113.651687l0.034451 0.03445q113.651687 113.651687 0 227.303374l-582.658679 582.658679q-113.651687 113.651687-227.303375 0l-0.03445-0.03445q-113.651687-113.651687 0-227.303374l582.658679-582.658679q113.651687-113.651687 227.303374 0Z"
                        p-id="6065" />
                    </svg>
                  </i>
                </div>
              </div>
              <div class="flex space-x-2">
                <el-button plain type="primary" @click="handleViewClick" v-if="aiAnalysis !== ''">
                  查看
                </el-button>
                <!-- <el-button plain type="primary" @click="generateAiAnalysis" :loading="aiLoading">
                  生成AI意见
                </el-button> -->
                <el-button plain type="success" @click="saveAiAnalysis"> 保存 </el-button>
              </div>
            </div>
            <div class="bg-gray-50 pl-6 pt-3 pb-3 rounded-lg text-sm leading-6">
              <div
class="text-gray-700" v-html="renderedAiAnalysis" v-if="aiAnalysis !== ''"
                style="max-height: 28vh; overflow-y: auto">
              </div>
              <div class="text-gray-700" v-else>暂无AI分析结果</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <el-dialog
v-model="dialogVisible" title="AI 分析过程和分析结果" width="70%" :before-close="closeDialog"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div>
        <!-- 思考中使用普通卡片显示，不折叠 -->
        <template v-if="!isAnalysisFinish">
          <el-card class="reasoning-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="flex items-center">
                  <h3>分析过程</h3>
                  <el-tooltip content="AI思考的过程" placement="top" class="ml-2">
                    <el-icon class="ml-1">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </div>
            </template>
            <div ref="reasoningContainer" class="reasoning-container">
              <div class="text-wrap text-base whitespace-pre-wrap reasoning-content">{{
                tempReasoning
              }}</div>
            </div>
          </el-card>
        </template>

        <!-- 思考完成后使用自定义折叠面板 -->
        <template v-else>
          <el-card class="custom-collapse-card reasoning-card" shadow="hover">
            <template #header>
              <div
class="custom-collapse-header"
                @click="isReasoningCollapsed = isReasoningCollapsed.includes('1') ? [] : ['1']">
                <div class="flex justify-between w-full pr-4">
                  <div class="flex items-center">
                    <h3 class="m-0">分析过程</h3>
                    <el-tooltip content="AI思考的过程" placement="top" class="ml-2">
                      <el-icon class="ml-1">
                        <InfoFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                  <div class="flex items-center">
                    <span class="text-sm text-gray-500 mr-2">
                      {{ isReasoningCollapsed.includes('1') ? '点击展开' : '点击折叠' }}
                    </span>
                    <el-icon
class="transition-all"
                      :class="isReasoningCollapsed.includes('1') ? 'transform rotate-180' : ''">
                      <ArrowDown />
                    </el-icon>
                  </div>
                </div>
              </div>
            </template>
            <div class="custom-collapse-content" :class="{ hidden: isReasoningCollapsed.includes('1') }">
              <div ref="reasoningContainer" class="reasoning-container">
                <div class="text-wrap text-base whitespace-pre-wrap reasoning-content">{{
                  tempReasoning
                }}</div>
              </div>
            </div>
          </el-card>
        </template>

        <el-divider />

        <el-card class="result-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="flex items-center">
                <h3>分析结果</h3>
                <el-tooltip content="AI分析的结论" placement="top" class="ml-2">
                  <el-icon class="ml-1">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </div>
          </template>
          <div ref="resultContainer" class="result-container">
            <div v-html="renderedTempResult" class="text-wrap leading-7 text-base result-content"></div>
          </div>
        </el-card>
      </div>
      <template #footer>
        <el-button @click="closeDialog">关闭</el-button>
      </template>
    </el-dialog>

    <el-dialog v-model="finishDialogVisible" title="AI 分析结果" width="70%">
      <div>
        <el-card class="custom-collapse-card reasoning-card" shadow="hover">
          <template #header>
            <div
class="custom-collapse-header"
              @click="isReasoningCollapsed = isReasoningCollapsed.includes('1') ? [] : ['1']">
              <div class="flex justify-between w-full pr-4">
                <div class="flex items-center">
                  <h3 class="m-0">分析过程</h3>
                  <el-tooltip content="AI思考的过程" placement="top" class="ml-2">
                    <el-icon class="ml-1">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
                <div class="flex items-center">
                  <span class="text-sm text-gray-500 mr-2">
                    {{ isReasoningCollapsed.includes('1') ? '点击展开' : '点击折叠' }}
                  </span>
                  <el-icon
class="transition-all"
                    :class="isReasoningCollapsed.includes('1') ? 'transform rotate-180' : ''">
                    <ArrowDown />
                  </el-icon>
                </div>
              </div>
            </div>
          </template>
          <div class="custom-collapse-content" :class="{ hidden: isReasoningCollapsed.includes('1') }">
            <div class="reasoning-container">
              <div class="text-wrap text-base whitespace-pre-wrap reasoning-content">{{
                aiReasoning
              }}</div>
            </div>
          </div>
        </el-card>

        <el-divider />

        <el-card class="result-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="flex items-center">
                <h3>分析结果</h3>
                <el-tooltip content="AI分析的结论" placement="top" class="ml-2">
                  <el-icon class="ml-1">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </div>
          </template>
          <div class="result-container">
            <div v-html="renderedAiResult" class="text-wrap leading-7 text-base result-content"></div>
          </div>
        </el-card>
      </div>
      <template #footer>
        <el-button @click="finishDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 添加对比弹窗 -->
    <el-dialog
v-model="compareDialogVisible" title="评估结果对比" width="90%" @closed="handleCompareDialogClose"
      :close-on-click-modal="false" :destroy-on-close="true">
      <div class="flex">
        <!-- 原评估结果 -->
        <div class="flex-1 mr-4">
          <el-card class="mb-4">
            <template #header>
              <div class="flex justify-between">
                <h3 class="font-bold">原评估结果</h3>
                <div class="eval-info">
                  <div class="info-item">
                    <span class="label">评估师：</span>
                    <span class="value">{{ originalResult?.evaluatorName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">评估原因：</span>
                    <span class="value">{{ originalResult?.evaluationReason }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">评估时间：</span>
                    <span class="value">{{ timestampToDate(originalResult?.evaluationTime) }}</span>
                  </div>
                </div>
              </div>
            </template>
            <div class="compare-form-container">
              <form-create
v-if="originalResult?.result" :rule="JSON.parse(originalResult.result).rules"
                :option="{ ...templateOption, disabled: true }" :value="JSON.parse(originalResult.result).formData"
                :disabled="true" />
            </div>
          </el-card>
        </div>

        <!-- 复评结果 -->
        <div class="flex-1">
          <el-card class="mb-4">
            <template #header>
              <div class="flex justify-between">
                <h3 class="font-bold">复评结果</h3>
                <div class="eval-info">
                  <div class="info-item">
                    <span class="label">评估师：</span>
                    <span class="value">{{ evaluationRecord?.evaluatorName }}</span>
                  </div>

                  <div class="info-item">
                    <span class="label">评估时间：</span>
                    <span class="value">{{
                      timestampToDate(evaluationRecord?.evaluationTime)
                    }}</span>
                  </div>
                </div>
              </div>
            </template>
            <div class="compare-form-container">
              <form-create
v-if="evaluationRecord?.result" :rule="templateRule"
                :option="{ ...templateOption, disabled: true }" :value="previewForm" :disabled="true" />
            </div>
          </el-card>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.header-card {
  margin-bottom: 5px;
  /* 添加底部间距 */
}

.footer-card {
  margin-top: 16px;
  /* 添加顶部间距 */
}

.menu {
  /* flex-grow: 1;  */
  text-align: center;
  /* 中间对齐 */
}

.el-menu {
  border-bottom: none;
}

/* 移除菜单项的边框 */
:deep(.el-menu-item) {
  border-bottom: none !important;
}

/* 调整菜单项的高度以匹配 header */
:deep(.el-menu--horizontal > .el-menu-item) {
  height: var(--el-header-height);
  line-height: var(--el-header-height);
}

.evaluation-card {
  transition: all 0.3s;
}

.evaluation-card:hover {
  transform: translateY(-2px);
}

.el-menu--horizontal {
  --el-menu-horizontal-height: 30px;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.think-content {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #409eff;
}

// .eval-info {
//   display: flex;
//   align-items: center;
//   gap: 32px;
//   padding: 4px 8px;

//   .info-item {
//     display: flex;
//     align-items: center;

//     .label {
//       color: var(--el-text-color-secondary);
//       margin-right: 8px;
//       font-size: 14px;
//     }

//     .value {
//       color: var(--el-text-color-primary);
//       font-size: 14px;
//       font-weight: 500;
//     }
//   }
// }

.reasoning-card,
.result-card {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .reasoning-container,
  .result-container {
    height: 350px;
    overflow-y: auto;
    padding: 8px;
    border-radius: 4px;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
    scroll-behavior: smooth;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #909399;
    }
  }

  .reasoning-content,
  .result-content {
    animation: fadeIn 0.5s ease-in-out;
    line-height: 1.6;
  }
}

/* 左侧表单容器宽度控制 */
.form-container {
  max-width: 75%;
  /* 限制左侧容器最大宽度 */

  .form-content-container {
    max-width: 100%;
    word-wrap: break-word;
    word-break: break-all;

    /* 确保表单内容不会超出容器宽度 */
    :deep(.el-form) {
      max-width: 100%;
    }

    :deep(.el-form-item) {
      max-width: 100%;

      .el-form-item__label {
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
        line-height: 1.4;
      }

      .el-form-item__content {
        max-width: 100%;

        .el-input,
        .el-textarea,
        .el-select,
        .el-radio-group,
        .el-checkbox-group {
          max-width: 100%;
        }

        .el-input__inner,
        .el-textarea__inner {
          word-wrap: break-word;
          word-break: break-all;
        }
      }
    }
  }
}

/* 左右卡片高度一致的样式 */
.left-card,
.right-card {
  height: 83vh;
  display: flex;
  flex-direction: column;
}

.left-card :deep(.el-card__body),
.right-card :deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 100%;
}

.form-content-container {
  flex: 1;
  overflow-y: auto;
  max-height: calc(83vh - 120px);
}

/* 右侧卡片内容区域允许滚动 */
.right-card .eval-info,
.right-card .flex.flex-col,
.right-card .mt-1.space-y-4,
.right-card .mt-1 {
  overflow-y: visible;
}

/* 右侧卡片内容区域自适应高度 */
.right-card> :deep(.el-card__body) {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  padding: 16px;

  /* 美化滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #909399;
  }
}

/* 右侧卡片内容区域布局 */
.right-card .eval-info {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.right-card .mt-1.space-y-4 {
  flex-shrink: 0;
  margin-bottom: 16px;
}

/* 只有评估师分析和AI分析区域保留滚动，但隐藏滚动条 */
.right-card .mt-4 {
  overflow-y: auto;
  flex: 1;

  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;

  /* IE and Edge */
  &::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
  }
}

/* 确保右侧卡片的直接子元素正确布局 */
.right-card> :deep(.el-card__body)>* {
  flex-shrink: 0;
}

.right-card> :deep(.el-card__body)>div:last-child {
  flex: 1;
  overflow: visible;
}

/* 确保AI分析内容区域在卡片内滚动，但隐藏滚动条 */
.right-card .bg-gray-50 {
  max-height: 28vh;
  overflow-y: auto;

  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;

  /* IE and Edge */
  &::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
  }
}

.result-card {
  .result-container {
    background-color: #f0f9eb;

    &:hover {
      background-color: #e6f7df;
    }
  }

  .result-content {
    padding: 0 12px;

    :deep(h1),
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
      margin-top: 16px;
      margin-bottom: 8px;
      font-weight: 600;
      color: #303133;
    }

    :deep(p) {
      margin-bottom: 12px;
    }

    :deep(ul),
    :deep(ol) {
      padding-left: 24px;
      margin-bottom: 12px;
    }

    :deep(blockquote) {
      padding: 0 12px;
      color: #606266;
      border-left: 4px solid #dcdfe6;
      margin: 16px 0;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0.7;
  }

  to {
    opacity: 1;
  }
}

.custom-collapse-card {
  .custom-collapse-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.3s;
    padding: 8px 0px;
    border-radius: 4px;

    // &:hover {
    //   background-color: #f5f7fa;
    // }
  }

  .custom-collapse-content {
    transition: all 0.3s ease-in-out;
    overflow: hidden;

    &.hidden {
      max-height: 0;
      padding: 0;
      opacity: 0;
    }
  }
}

/* 右侧信息容器宽度控制 */
.info-container {
  min-width: 25%;
  /* 确保右侧容器最小宽度 */
  max-width: 25%;
  /* 限制右侧容器最大宽度 */
}

/* 对比弹窗中的表单容器宽度控制 */
.compare-form-container {
  max-width: 100%;
  word-wrap: break-word;
  word-break: break-all;

  /* 确保表单内容不会超出容器宽度 */
  :deep(.el-form) {
    max-width: 100%;
  }

  :deep(.el-form-item) {
    max-width: 100%;

    .el-form-item__label {
      word-wrap: break-word;
      word-break: break-all;
      white-space: normal;
      line-height: 1.4;
    }

    .el-form-item__content {
      max-width: 100%;

      .el-input,
      .el-textarea,
      .el-select,
      .el-radio-group,
      .el-checkbox-group {
        max-width: 100%;
      }

      .el-input__inner,
      .el-textarea__inner {
        word-wrap: break-word;
        word-break: break-all;
      }
    }
  }
}

.eval-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding-bottom: 5px;
  margin-bottom: 5px;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 5px;

    .label {
      color: var(--el-text-color-secondary);
      margin-right: 8px;
      font-size: 14px;
    }

    .value {
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-weight: 500;
    }
  }
}
</style>
