<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="700px" @close="handleClose">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="房间信息" v-if="presetRoomInfo">
        <el-input
          :value="`${presetRoomInfo.buildingName} - ${presetRoomInfo.roomNumber} (${presetRoomInfo.floor}层)`"
          disabled
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="床铺号" prop="bedNumber">
        <el-input
          v-model="formData.bedNumber"
          placeholder="请输入床铺号"
          class="!w-240px"
          :disabled="formType === 'view'"
        />
      </el-form-item>
      <el-form-item label="床铺类型" prop="bedType">
        <el-select
          v-model="formData.bedType"
          placeholder="请选择床铺类型"
          class="!w-240px"
          :disabled="formType === 'view'"
        >
          <el-option label="单人床" :value="1" />
          <el-option label="双人床" :value="2" />
          <el-option label="医疗床" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="床铺状态" prop="status">
        <el-select
          v-model="formData.status"
          placeholder="请选择床铺状态"
          class="!w-240px"
          :disabled="formType === 'view'"
        >
          <el-option label="空闲" :value="1" />
          <el-option label="已占用" :value="2" />
          <el-option label="维修中" :value="3" />
          <el-option label="停用" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="入住人姓名" prop="occupantName" v-if="formData.status === 2">
        <el-input
          v-model="formData.occupantName"
          placeholder="请输入入住人姓名"
          class="!w-240px"
          :disabled="formType === 'view'"
        />
      </el-form-item>
      <el-form-item label="床铺描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入床铺描述"
          :rows="3"
          class="!w-400px"
          :disabled="formType === 'view'"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button v-if="formType !== 'view'" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { BedService, BedVO } from '@/api/institution/bed'

defineOptions({ name: 'BedForm' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中
const formType = ref('') // 表单的类型
const presetRoomInfo = ref(null) // 预设的房间信息

const formData = ref<BedVO>({
  id: undefined,
  buildingId: undefined,
  buildingName: '',
  floor: undefined,
  roomId: undefined,
  roomNumber: '',
  bedNumber: '',
  bedType: undefined,
  status: 1,
  occupantId: undefined,
  occupantName: '',
  description: '',
  createTime: new Date()
})

const formRules = reactive({
  bedNumber: [{ required: true, message: '床铺号不能为空', trigger: 'blur' }],
  bedType: [{ required: true, message: '床铺类型不能为空', trigger: 'change' }],
  status: [{ required: true, message: '床铺状态不能为空', trigger: 'change' }],
  occupantName: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (formData.value.status === 2 && (!value || value.trim() === '')) {
          callback(new Error('已占用状态下入住人姓名不能为空'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

/** 打开弹窗 */
const open = async (type: string, id?: number, roomInfo?: any) => {
  dialogVisible.value = true
  dialogTitle.value = getDialogTitle(type)
  formType.value = type
  resetForm()

  // 预设房间信息（新增床铺时使用）
  if (roomInfo && type === 'create') {
    presetRoomInfo.value = roomInfo
    formData.value.roomId = roomInfo.roomId
    formData.value.roomNumber = roomInfo.roomNumber
    formData.value.buildingId = roomInfo.buildingId
    formData.value.buildingName = roomInfo.buildingName
    formData.value.floor = roomInfo.floor
  } else {
    presetRoomInfo.value = null
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await BedService.getBed(id)
    } catch (error) {
      console.error('获取床铺详情失败:', error)
      message.error('获取床铺详情失败')
    } finally {
      formLoading.value = false
    }
  }
}

/** 获取弹窗标题 */
const getDialogTitle = (type: string) => {
  switch (type) {
    case 'create':
      return '新增床铺'
    case 'update':
      return '编辑床铺'
    case 'view':
      return '查看床铺'
    default:
      return '床铺信息'
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const formRef = ref()
const submitForm = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    formLoading.value = true
    try {
      const data = formData.value
      if (formType.value === 'create') {
        await BedService.createBed(data)
        message.success('新增床铺成功')
      } else {
        await BedService.updateBed(data)
        message.success('修改床铺成功')
      }
      dialogVisible.value = false
      // 发送操作成功的事件
      emit('success')
    } catch (error) {
      console.error('保存床铺失败:', error)
      message.error('保存床铺失败')
    } finally {
      formLoading.value = false
    }
  })
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    buildingId: undefined,
    buildingName: '',
    floor: undefined,
    roomId: undefined,
    roomNumber: '',
    bedNumber: '',
    bedType: undefined,
    status: 1,
    occupantId: undefined,
    occupantName: '',
    description: '',
    createTime: new Date()
  }
}

/** 关闭弹窗 */
const handleClose = () => {
  resetForm()
}

defineExpose({ open }) // 提供 open 方法
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
