<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="楼宇名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入楼宇名称"
          :disabled="formType === 'view'"
          class="!w-400px"
          @blur="handleNameBlur"
        />
        <div v-if="nameError" class="text-red-500 text-sm mt-1">
          {{ nameError }}
        </div>
      </el-form-item>
      <el-form-item label="楼层数量" prop="floorCount">
        <el-input-number
          v-model="formData.floorCount"
          placeholder="请输入楼层数量"
          :disabled="formType === 'view'"
          :min="1"
          :max="50"
          class="!w-400px"
        />
      </el-form-item>
      <el-form-item label="楼宇地址" prop="address">
        <el-input
          v-model="formData.address"
          placeholder="请输入楼宇地址"
          :disabled="formType === 'view'"
          class="!w-400px"
        />
      </el-form-item>
      <el-form-item label="楼宇描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入楼宇描述"
          :disabled="formType === 'view'"
          class="!w-400px"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          v-if="formType !== 'view'"
          :loading="formLoading"
          type="primary"
          @click="submitForm"
        >
          确 定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { BuildingService } from '@/api/institution/building'

/** 楼宇表单 */
defineOptions({ name: 'BuildingForm' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改；view - 查看
const nameError = ref('') // 楼宇名称错误信息
const formData = ref({
  id: undefined,
  name: '',
  floorCount: 1,
  address: '',
  description: ''
})

const formRules = ref({
  name: [
    { required: true, message: '楼宇名称不能为空', trigger: 'blur' },
    { min: 1, max: 50, message: '楼宇名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  floorCount: [
    { required: true, message: '楼层数量不能为空', trigger: 'blur' },
    { type: 'number', min: 1, max: 50, message: '楼层数量必须在 1 到 50 之间', trigger: 'blur' }
  ],
  address: [
    { max: 200, message: '楼宇地址长度不能超过 200 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '楼宇描述长度不能超过 500 个字符', trigger: 'blur' }
  ]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = getDialogTitle(type)
  formType.value = type
  resetForm()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await BuildingService.getBuilding(id)
      formData.value = {
        id: data.id,
        name: data.name,
        floorCount: data.floorCount,
        address: data.address || '',
        description: data.description || ''
      }
    } catch (error) {
      console.error('获取楼宇详情失败:', error)
      message.error('获取楼宇详情失败')
    } finally {
      formLoading.value = false
    }
  }
}

/** 获取弹窗标题 */
const getDialogTitle = (type: string) => {
  switch (type) {
    case 'create':
      return '新增楼宇'
    case 'update':
      return '修改楼宇'
    case 'view':
      return '查看楼宇'
    default:
      return '楼宇信息'
  }
}

/** 检查楼宇名称是否已存在 */
const checkBuildingNameExists = async (name: string, excludeId?: number) => {
  try {
    const params = { name }
    const data = await BuildingService.getBuildingList(params)
    const existingBuildings = Array.isArray(data) ? data : (data.list || [])

    // 如果是编辑模式，排除当前楼宇
    const filteredBuildings = excludeId
      ? existingBuildings.filter((building: any) => building.id !== excludeId)
      : existingBuildings

    return filteredBuildings.length > 0
  } catch (error) {
    console.error('检查楼宇名称是否存在失败:', error)
    return false
  }
}

/** 楼宇名称失焦检查 */
const handleNameBlur = async () => {
  nameError.value = '' // 清除之前的错误信息

  // 检查楼宇名称是否为空
  if (!formData.value.name || !formData.value.name.trim()) {
    return
  }

  // 检查楼宇名称是否已存在
  const exists = await checkBuildingNameExists(
    formData.value.name.trim(),
    formType.value === 'update' ? formData.value.id : undefined
  )

  if (exists) {
    nameError.value = `楼宇名称 "${formData.value.name}" 已存在`
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return

  // 检查是否有楼宇名称错误信息
  if (nameError.value) {
    message.error('请修正楼宇名称错误后再提交')
    return
  }

  // 最终检查楼宇名称是否已存在
  if (formData.value.name && formData.value.name.trim()) {
    const exists = await checkBuildingNameExists(
      formData.value.name.trim(),
      formType.value === 'update' ? formData.value.id : undefined
    )
    if (exists) {
      nameError.value = `楼宇名称 "${formData.value.name}" 已存在`
      message.error(`楼宇名称 "${formData.value.name}" 已存在，请使用其他名称`)
      return
    }
  }

  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      await BuildingService.createBuilding(data)
      message.success('新增楼宇成功')
    } else {
      await BuildingService.updateBuilding(data)
      message.success('修改楼宇成功')
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (error) {
    console.error('提交楼宇信息失败:', error)
    message.error('提交楼宇信息失败')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    floorCount: 1,
    address: '',
    description: ''
  }
  nameError.value = '' // 清除楼宇名称错误信息
  formRef.value?.resetFields()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
.el-input-number {
  width: 100%;
}
</style>
