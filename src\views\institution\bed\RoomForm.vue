<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="700px" @close="handleClose">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="楼层" prop="floor">
        <el-select
          v-model="formData.floor"
          placeholder="请选择楼层"
          class="!w-240px"
          :disabled="formType === 'view'"
          @change="handleFloorChange"
        >
          <el-option
            v-for="floor in floorList"
            :key="floor"
            :label="floor + '层'"
            :value="floor"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="房间号" prop="roomNumber">
        <el-input
          v-model="formData.roomNumber"
          placeholder="请输入房间号"
          class="!w-240px"
          :disabled="formType === 'view'"
          @blur="handleRoomNumberBlur"
        />
        <div v-if="roomNumberError" class="text-red-500 text-sm mt-1">
          {{ roomNumberError }}
        </div>
      </el-form-item>
      <el-form-item label="房间类型" prop="type">
        <el-select
          v-model="formData.type"
          placeholder="请选择房间类型"
          class="!w-240px"
          :disabled="formType === 'view'"
        >
          <el-option label="单人间" :value="1" />
          <el-option label="双人间" :value="2" />
          <el-option label="活动室" :value="3" />
          <el-option label="餐厅" :value="4" />
          <el-option label="医疗室" :value="5" />
        </el-select>
      </el-form-item>
      <el-form-item label="床位数量" prop="bedCount">
        <el-input-number
          v-model="formData.bedCount"
          :min="0"
          :max="10"
          class="!w-240px"
          :disabled="formType === 'view'"
        />
      </el-form-item>
      <el-form-item label="房间容量" prop="capacity">
        <el-input-number
          v-model="formData.capacity"
          :min="0"
          :max="100"
          class="!w-240px"
          :disabled="formType === 'view'"
        />
      </el-form-item>
      <el-form-item label="房间状态" prop="status">
        <el-select
          v-model="formData.status"
          placeholder="请选择房间状态"
          class="!w-240px"
          :disabled="formType === 'view'"
        >
          <el-option label="正常" :value="1" />
          <el-option label="维修中" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="房间设备" prop="facilities">
        <el-input
          v-model="formData.facilities"
          type="textarea"
          placeholder="请输入房间设备"
          :rows="3"
          :disabled="formType === 'view'"
        />
      </el-form-item>
      <el-form-item label="房间描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入房间描述"
          :rows="3"
          :disabled="formType === 'view'"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button v-if="formType !== 'view'" type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { RoomService, RoomVO } from '@/api/institution/room'
import { BuildingService, BuildingVO } from '@/api/institution/building'

defineOptions({ name: 'RoomForm' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中
const formType = ref('') // 表单的类型
const formData = ref<RoomVO>({
  id: undefined,
  buildingId: undefined,
  buildingName: '',
  floor: undefined,
  roomNumber: '',
  type: undefined,
  bedCount: 0,
  capacity: 0,
  status: 1,
  facilities: '',
  description: '',
  createTime: new Date()
})
const formRules = ref({
  buildingId: [{ required: true, message: '请选择楼宇', trigger: 'change' }],
  floor: [{ required: true, message: '请选择楼层', trigger: 'change' }],
  roomNumber: [{ required: true, message: '请输入房间号', trigger: 'blur' }],
  type: [{ required: true, message: '请选择房间类型', trigger: 'change' }],
  bedCount: [{ required: true, message: '请输入床位数量', trigger: 'blur' }],
  capacity: [{ required: true, message: '请输入房间容量', trigger: 'blur' }],
  status: [{ required: true, message: '请选择房间状态', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

const buildingList = ref<BuildingVO[]>([]) // 楼宇列表
const floorList = ref<number[]>([]) // 楼层列表
const isPresetBuilding = ref(false) // 是否预设了楼宇
const roomNumberError = ref('') // 房间号错误信息

/** 打开弹窗 */
const open = async (type: string, id?: number, presetBuildingId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = getDialogTitle(type)
  formType.value = type
  resetForm()

  // 预设楼宇ID（新增房间时使用）
  if (presetBuildingId && type === 'create') {
    formData.value.buildingId = presetBuildingId
    isPresetBuilding.value = true
  } else {
    isPresetBuilding.value = false
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await RoomService.getRoom(id)
    } catch (error) {
      console.error('获取房间详情失败:', error)
      message.error('获取房间详情失败')
    } finally {
      formLoading.value = false
    }
  }
  // 获取楼宇列表
  await getBuildingList()

  // 如果预设了楼宇ID，获取对应的楼层列表
  if (presetBuildingId && type === 'create') {
    await handleBuildingChange(presetBuildingId)
  }
}

/** 获取弹窗标题 */
const getDialogTitle = (type: string) => {
  switch (type) {
    case 'create':
      return '新增房间'
    case 'update':
      return '修改房间'
    case 'view':
      return '查看房间'
    default:
      return '房间信息'
  }
}

/** 获取楼宇列表 */
const getBuildingList = async () => {
  try {
    const data = await BuildingService.getBuildingList({})
    buildingList.value = data
  } catch (error) {
    console.error('获取楼宇列表失败:', error)
    message.error('获取楼宇列表失败')
  }
}

/** 楼宇变更 */
const handleBuildingChange = async (buildingId: number) => {
  formData.value.floor = undefined
  roomNumberError.value = '' // 清除房间号错误信息
  if (buildingId) {
    try {
      const building = await BuildingService.getBuilding(buildingId)
      const floorCount = building.floorCount || building.floors || 1
      floorList.value = Array.from({ length: floorCount }, (_, i) => i + 1)
    } catch (error) {
      console.error('获取楼层列表失败:', error)
      message.error('获取楼层列表失败')
    }
  } else {
    floorList.value = []
  }
}

/** 楼层变更 */
const handleFloorChange = () => {
  roomNumberError.value = '' // 清除房间号错误信息
  // 如果房间号已填写，重新检查
  if (formData.value.roomNumber) {
    handleRoomNumberBlur()
  }
}

/** 房间号失焦检查 */
const handleRoomNumberBlur = async () => {
  roomNumberError.value = '' // 清除之前的错误信息

  // 检查必要字段是否已填写
  if (!formData.value.buildingId || !formData.value.floor || !formData.value.roomNumber) {
    return
  }

  // 检查房间是否已存在
  const exists = await checkRoomExists(
    formData.value.buildingId,
    formData.value.floor,
    formData.value.roomNumber,
    formType.value === 'update' ? formData.value.id : undefined
  )

  if (exists) {
    roomNumberError.value = `房间 ${formData.value.roomNumber} 在该楼层已存在`
  }
}

/** 检查房间是否已存在 */
const checkRoomExists = async (buildingId: number, floor: number, roomNumber: string, excludeId?: number) => {
  try {
    const params = {
      buildingId,
      floor,
      roomNumber
    }
    const data = await RoomService.getRoomList(params)
    const existingRooms = Array.isArray(data) ? data : (data.list || [])

    // 如果是编辑模式，排除当前房间
    const filteredRooms = excludeId
      ? existingRooms.filter((room: any) => room.id !== excludeId)
      : existingRooms

    return filteredRooms.length > 0
  } catch (error) {
    console.error('检查房间是否存在失败:', error)
    return false
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return

  // 检查是否有房间号错误信息
  if (roomNumberError.value) {
    message.error('请修正房间号错误后再提交')
    return
  }

  // 最终检查房间是否已存在
  if (formType.value === 'create') {
    const exists = await checkRoomExists(
      formData.value.buildingId!,
      formData.value.floor!,
      formData.value.roomNumber
    )
    if (exists) {
      roomNumberError.value = `房间 ${formData.value.roomNumber} 在该楼层已存在`
      message.error(`房间 ${formData.value.roomNumber} 在该楼层已存在，请使用其他房间号`)
      return
    }
  } else if (formType.value === 'update') {
    // 编辑时也要检查，但排除当前房间
    const exists = await checkRoomExists(
      formData.value.buildingId!,
      formData.value.floor!,
      formData.value.roomNumber,
      formData.value.id
    )
    if (exists) {
      roomNumberError.value = `房间 ${formData.value.roomNumber} 在该楼层已存在`
      message.error(`房间 ${formData.value.roomNumber} 在该楼层已存在，请使用其他房间号`)
      return
    }
  }

  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      await RoomService.createRoom(data)
      message.success(t('common.createSuccess'))
    } else {
      await RoomService.updateRoom(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 通知父组件，进行刷新
    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
    message.error('提交表单失败')
  } finally {
    formLoading.value = false
  }
}



/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    buildingId: undefined,
    buildingName: '',
    floor: undefined,
    roomNumber: '',
    type: undefined,
    bedCount: 0,
    capacity: 0,
    status: 1,
    facilities: '',
    description: '',
    createTime: new Date()
  }
  floorList.value = []
  isPresetBuilding.value = false
  roomNumberError.value = '' // 清除房间号错误信息
}

/** 暴露方法 */
defineExpose({ open })
</script>