<template>
  <div class="bed-management">
    <ContentWrap>
      <!-- 面包屑导航 -->
      <div class="breadcrumb-container">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            <el-button type="primary" link @click="goBack" class="breadcrumb-button">
              <Icon icon="ep:arrow-left" class="mr-5px" />
              返回房间列表
            </el-button>
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{ buildingName }} - 第{{ floor }}层 - {{ roomNumber }} - 床铺管理</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 搜索工作栏 -->
      <div class="filter-container">
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="80px"
        >
          <el-form-item label="床铺号" prop="bedNumber">
            <el-input
              v-model="queryParams.bedNumber"
              placeholder="请输入床铺号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="床铺类型" prop="bedType">
            <el-select
              v-model="queryParams.bedType"
              placeholder="请选择床铺类型"
              clearable
              class="!w-240px"
            >
              <el-option label="单人床" :value="1" />
              <el-option label="双人床" :value="2" />
              <el-option label="医疗床" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="床铺状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择床铺状态"
              clearable
              class="!w-240px"
            >
              <el-option label="空闲" :value="1" />
              <el-option label="已占用" :value="2" />
              <el-option label="维修中" :value="3" />
              <el-option label="停用" :value="4" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button type="primary" @click="handleAdd">
              <Icon icon="ep:plus" class="mr-5px" /> 新增床铺
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </ContentWrap>

    <!-- 床铺卡片列表 -->
    <ContentWrap>
      <div class="bed-list" v-loading="loading">
        <el-row :gutter="20">
          <el-col
            v-for="item in list"
            :key="item.id"
            :span="6"
            class="mb-20"
          >
            <el-card
              :class="['bed-card', getBedStatusClass(item.status)]"
              shadow="hover"
            >
              <template #header>
                <div class="card-header">
                  <el-tooltip :content="item.bedNumber" placement="top" :show-after="500">
                    <span class="bed-name">{{ item.bedNumber }}</span>
                  </el-tooltip>
                  <div class="bed-badges">
                    <el-tag size="small" :type="getBedTypeTag(item.bedType)" style="white-space: nowrap;">
                      {{ getBedTypeName(item.bedType) }}
                    </el-tag>
                    <el-tag
                      size="small"
                      :type="getBedStatusType(item.status)"
                      class="ml-5"
                      style="white-space: nowrap;"
                    >
                      {{ getBedStatusName(item.status) }}
                    </el-tag>
                  </div>
                </div>
              </template>
              <div class="bed-content">
                <div class="bed-info">
                  <p class="bed-room">房间: {{ item.roomNumber }}</p>
                  <p class="bed-floor">楼层: {{ item.floor }}层</p>
                  <p class="bed-occupant" v-if="item.occupantName">入住人: {{ item.occupantName }}</p>
                  <p class="bed-occupant" v-else>入住人: 暂无</p>
                  <p class="bed-description" v-if="item.description">描述: {{ item.description }}</p>
                </div>
                <div class="bed-actions">
                  <el-button
                    type="primary"
                    link
                    @click="handleEdit(item)"
                  >
                    <Icon icon="ep:edit" class="mr-5px" /> 编辑
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    @click="handleView(item)"
                  >
                    <Icon icon="ep:view" class="mr-5px" /> 查看
                  </el-button>
                  <el-button
                    type="danger"
                    link
                    @click="handleDelete(item)"
                  >
                    <Icon icon="ep:delete" class="mr-5px" /> 删除
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 分页 -->
        <div class="pagination-container" v-if="total > 0">
          <el-pagination
            v-model:current-page="queryParams.pageNo"
            v-model:page-size="queryParams.pageSize"
            :total="total"
            :page-sizes="[8, 16, 24, 32]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="getList"
            @current-change="getList"
          />
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && list.length === 0" class="empty-state">
          <el-empty description="该房间暂无床铺数据" />
        </div>
      </div>
    </ContentWrap>
  </div>

  <!-- 表单弹窗：编辑 -->
  <BedForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { BedService } from '@/api/institution/bed'
import BedForm from './BedForm.vue'

/** 床铺管理 */
defineOptions({ name: 'BedManagement' })

const message = useMessage() // 消息弹窗
const router = useRouter() // 路由
const route = useRoute() // 当前路由

const loading = ref(true) // 加载中
const list = ref<any[]>([]) // 列表数据
const total = ref(0) // 总数

// 从路由参数获取房间信息
const roomId = ref(Number(route.query.roomId) || 0)
const roomNumber = ref((route.query.roomNumber as string) || '未知房间')
const buildingId = ref(Number(route.query.buildingId) || 0)
const buildingName = ref((route.query.buildingName as string) || '未知楼宇')
const floor = ref(Number(route.query.floor) || 1)

/** 查询参数 */
const queryParams = ref({
  pageNo: 1,
  pageSize: 8, // 每页显示8个卡片（2行x4列）
  roomId: roomId.value,
  bedNumber: undefined as string | undefined,
  bedType: undefined as number | undefined,
  status: undefined as number | undefined
})

/** 查询床铺列表 */
const getList = async () => {
  loading.value = true
  try {
    // 获取床铺列表
    const data = await BedService.getBedList(queryParams.value)
    // 处理分页数据
    if (data && typeof data === 'object' && 'list' in data) {
      // 分页数据格式
      list.value = data.list || []
      total.value = data.total || 0
    } else if (Array.isArray(data)) {
      // 数组格式（兼容旧版本）
      list.value = data
      total.value = data.length
    } else {
      // 其他格式
      list.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取床铺列表失败:', error)
    message.error('获取床铺列表失败')
    list.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/** 返回房间列表 */
const goBack = () => {
  router.push({
    path: '/institution/building/rooms',
    query: {
      buildingId: buildingId.value,
      buildingName: buildingName.value,
      floor: floor.value
    }
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 8,
    roomId: roomId.value,
    bedNumber: undefined as string | undefined,
    bedType: undefined as number | undefined,
    status: undefined as number | undefined
  }
  handleQuery()
}

/** 获取床铺类型名称 */
const getBedTypeName = (type: number) => {
  const typeMap = {
    1: '单人床',
    2: '双人床',
    3: '医疗床'
  }
  return typeMap[type] || '未知'
}

/** 获取床铺类型标签类型 */
const getBedTypeTag = (type: number) => {
  const typeMap = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return typeMap[type] || 'info'
}

/** 获取床铺状态名称 */
const getBedStatusName = (status: number) => {
  const statusMap = {
    1: '空闲',
    2: '已占用',
    3: '维修中',
    4: '停用'
  }
  return statusMap[status] || '未知'
}

/** 获取床铺状态标签类型 */
const getBedStatusType = (status: number) => {
  const statusMap = {
    1: 'success', // 空闲
    2: 'danger',  // 已占用
    3: 'warning', // 维修中
    4: 'info'     // 停用
  }
  return statusMap[status] || 'info'
}

/** 获取床铺状态样式类 */
const getBedStatusClass = (status: number) => {
  const statusMap = {
    1: 'bed-available',  // 空闲
    2: 'bed-occupied',   // 已占用
    3: 'bed-maintenance', // 维修中
    4: 'bed-disabled'    // 停用
  }
  return statusMap[status] || ''
}

/** 新增操作 */
const formRef = ref()
const handleAdd = () => {
  // 新增时预设房间信息
  formRef.value.open('create', undefined, {
    roomId: roomId.value,
    roomNumber: roomNumber.value,
    buildingId: buildingId.value,
    buildingName: buildingName.value,
    floor: floor.value
  })
}

/** 编辑操作 */
const handleEdit = (row: any) => {
  formRef.value.open('update', row.id)
}

/** 查看操作 */
const handleView = (row: any) => {
  formRef.value.open('view', row.id)
}

/** 删除操作 */
const handleDelete = async (row: any) => {
  try {
    // 二次确认
    await message.delConfirm()
    // 发起删除
    await BedService.deleteBed(row.id)
    message.success('删除床铺成功')
    // 刷新列表
    await getList()
  } catch (error) {
    console.error('删除床铺失败:', error)
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.bed-management {
  .breadcrumb-container {
    margin-bottom: 20px;

    .el-breadcrumb {
      font-size: 16px;

      .el-breadcrumb-item {
        font-size: 16px;
      }

      .breadcrumb-button {
        padding: 0;
        height: auto;
        line-height: 1;
        vertical-align: baseline;
        font-size: 16px;

        .mr-5px {
          margin-right: 5px;
        }
      }
    }
  }

  .filter-container {
    margin-bottom: 20px;
  }

  .bed-list {
    .mb-20 {
      margin-bottom: 20px;
    }

    .bed-card {
      min-height: 280px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      flex-direction: column;
      width: 100%;
      box-sizing: border-box;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 2px 15px 0 rgba(0, 0, 0, 0.1);

      }

      // 床铺状态样式
      &.bed-available {
        border-color: #67C23A;
        &:hover {
          border-color: #67C23A;
        }
      }

      &.bed-occupied {
        border-color: #F56C6C;
        &:hover {
          border-color: #F56C6C;
        }
      }

      &.bed-maintenance {
        border-color: #E6A23C;
        &:hover {
          border-color: #E6A23C;
        }
      }

      &.bed-disabled {
        border-color: #909399;
        opacity: 0.7;
        &:hover {
          border-color: #909399;
        }
      }

      .card-header {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .bed-name {
          font-weight: bold;
          font-size: 18px;
          width: 100%;
          margin-bottom: 8px;
          word-break: break-word;
          line-height: 1.3;
          color: #303133;
        }

        .bed-badges {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          gap: 5px;
          width: 100%;
          justify-content: flex-start;
        }
      }

      .bed-content {
        padding: 10px 0;
        flex: 1;
        display: flex;
        flex-direction: column;

        .bed-info {
          margin-bottom: 15px;
          flex: 1;

          p {
            margin: 8px 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.4;
          }

          .bed-room {
            color: #409EFF;
            font-weight: 500;
          }

          .bed-floor {
            color: #67C23A;
          }

          .bed-occupant {
            color: #E6A23C;
          }

          .bed-description {
            color: #909399;
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .bed-actions {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          margin-top: auto;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 30px;
    display: flex;
    justify-content: center;
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }
}

.el-tag {
  margin-right: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.mr-5px {
  margin-right: 5px;
}
</style>
