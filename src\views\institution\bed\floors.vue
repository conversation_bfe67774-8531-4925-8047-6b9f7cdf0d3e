<template>
  <div class="floor-management">
    <!-- 面包屑导航 -->
    <ContentWrap>
      <div class="breadcrumb-container">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            <el-button type="primary" link @click="goBack" class="breadcrumb-button">
              <Icon icon="ep:arrow-left" class="mr-5px" /> 返回楼宇列表
            </el-button>
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{ buildingName }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </ContentWrap>

    <!-- 楼层卡片列表 -->
    <ContentWrap>
      <div class="floor-list" v-loading="loading">
        <el-row :gutter="20">
          <el-col
            v-for="floor in floorList"
            :key="floor"
            :span="6"
            class="mb-20"
          >
            <el-card
              :class="['floor-card']"
              shadow="hover"
              @click="handleFloorClick(floor)"
            >
              <template #header>
                <div class="card-header">
                  <el-tooltip :content="`第${floor}层`" placement="top" :show-after="500">
                    <span class="floor-name">第{{ floor }}层</span>
                  </el-tooltip>
                  <div class="floor-badges">
                    <el-tag size="small" type="primary" style="white-space: nowrap;">
                      {{ getRoomCountByFloor(floor) }}个房间
                    </el-tag>
                  </div>
                </div>
              </template>
              <div class="floor-content">
                <div class="floor-info">
                  <p class="floor-number">楼层: 第{{ floor }}层</p>
                  <p class="floor-rooms">房间数量: {{ getRoomCountByFloor(floor) }}个</p>
                  <p class="floor-beds">床位数量: {{ getBedCountByFloor(floor) }}张</p>
                  <p class="floor-building">所属楼宇: {{ buildingName }}</p>
                </div>
                <div class="floor-actions">
                  <el-button
                    type="primary"
                    @click.stop="handleFloorClick(floor)"
                  >
                    <Icon icon="ep:view" class="mr-5px" /> 查看房间
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 空状态 -->
        <div v-if="!loading && floorList.length === 0" class="empty-state">
          <el-empty description="该楼宇暂无楼层数据" />
        </div>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { BuildingService } from '@/api/institution/building'
import { RoomService } from '@/api/institution/room'

/** 楼层管理 */
defineOptions({ name: 'FloorManagement' })

const message = useMessage() // 消息弹窗
const router = useRouter() // 路由
const route = useRoute() // 当前路由

const loading = ref(true) // 加载中
const floorList = ref<number[]>([]) // 楼层列表
const roomList = ref<any[]>([]) // 房间列表（用于统计）

// 从路由参数获取楼宇信息
const buildingId = ref(Number(route.query.buildingId) || 0)
const buildingName = ref(route.query.buildingName as string || '未知楼宇')

/** 获取楼层列表 */
const getFloorList = async () => {
  loading.value = true
  try {
    if (buildingId.value) {
      // 获取楼宇信息
      const building = await BuildingService.getBuilding(buildingId.value)
      const floorCount = building.floorCount || building.floors || 0
      
      // 生成楼层列表
      floorList.value = Array.from({ length: floorCount }, (_, i) => i + 1)
      
      // 获取该楼宇的所有房间用于统计
      await getRoomList()
    }
  } catch (error) {
    console.error('获取楼层列表失败:', error)
    message.error('获取楼层列表失败')
  } finally {
    loading.value = false
  }
}

/** 获取房间列表用于统计 */
const getRoomList = async () => {
  try {
    const data = await RoomService.getRoomList({
      pageNo: 1,
      pageSize: 1000, // 获取所有房间
      buildingId: buildingId.value
    })
    roomList.value = data.list || data || []
  } catch (error) {
    console.error('获取房间列表失败:', error)
  }
}

/** 获取指定楼层的房间数量 */
const getRoomCountByFloor = (floor: number) => {
  return roomList.value.filter(room => room.floor === floor).length
}

/** 获取指定楼层的床位数量 */
const getBedCountByFloor = (floor: number) => {
  return roomList.value
    .filter(room => room.floor === floor)
    .reduce((total, room) => total + (room.bedCount || 0), 0)
}

/** 返回楼宇列表 */
const goBack = () => {
  router.push({
    path: '/institution/bed'
  })
}

/** 点击楼层卡片 */
const handleFloorClick = (floor: number) => {
  // 跳转到房间列表页面，传递楼宇ID和楼层信息
  router.push({
    path: '/institution/building/rooms',
    query: {
      buildingId: buildingId.value,
      buildingName: buildingName.value,
      floor: floor
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getFloorList()
})
</script>

<style lang="scss" scoped>
.floor-management {
  .breadcrumb-container {
    margin-bottom: 20px;
    
    .el-breadcrumb {
      font-size: 16px;
      
      .el-breadcrumb-item {
        font-size: 16px;
      }
      
      .breadcrumb-button {
        padding: 0;
        height: auto;
        line-height: 1;
        vertical-align: baseline;
        font-size: 16px;
        
        .mr-5px {
          margin-right: 5px;
        }
      }
    }
  }

  .floor-list {
    .mb-20 {
      margin-bottom: 20px;
    }

    .floor-card {
      min-height: 280px;
      cursor: pointer;
      transition: all 0.3s;
      border: 2px solid transparent;
      display: flex;
      flex-direction: column;
      width: 100%;
      box-sizing: border-box;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 2px 15px 0 rgba(0, 0, 0, 0.1);
        border-color: var(--el-color-primary);
      }

      .card-header {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .floor-name {
          font-weight: bold;
          font-size: 18px;
          width: 100%;
          margin-bottom: 8px;
          word-break: break-word;
          line-height: 1.3;
          color: #303133;
        }

        .floor-badges {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          gap: 5px;
          width: 100%;
          justify-content: flex-start;
        }
      }

      .floor-content {
        padding: 10px 0;
        flex: 1;
        display: flex;
        flex-direction: column;

        .floor-info {
          margin-bottom: 15px;
          flex: 1;

          p {
            margin: 8px 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.4;
          }

          .floor-number {
            color: #409EFF;
            font-weight: 500;
          }

          .floor-rooms {
            color: #67C23A;
          }

          .floor-beds {
            color: #E6A23C;
          }

          .floor-building {
            color: #909399;
            font-size: 13px;
          }
        }

        .floor-actions {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          margin-top: auto;
        }
      }
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }
}

.el-tag {
  margin-right: 5px;
}

.mr-5px {
  margin-right: 5px;
}
</style>
