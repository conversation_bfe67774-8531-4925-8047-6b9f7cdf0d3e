<template>
  <div class="building-management">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <div class="filter-container">
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="80px"
        >
          <el-form-item label="楼宇名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入楼宇名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button type="primary" @click="handleAdd">
              <Icon icon="ep:plus" class="mr-5px" /> 新增楼宇
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </ContentWrap>

    <!-- 楼宇卡片列表 -->
    <ContentWrap>
      <div class="building-list" v-loading="loading">
        <el-row :gutter="20">
          <el-col
            v-for="item in list"
            :key="item.id"
            :span="6"
            class="mb-20"
          >
            <el-card
              :class="['building-card']"
              shadow="hover"
              @click="handleBuildingClick(item)"
            >
              <template #header>
                <div class="card-header">
                  <el-tooltip :content="item.name" placement="top" :show-after="500">
                    <span class="building-name">{{ item.name }}</span>
                  </el-tooltip>
                  <div class="building-badges">
                    <el-tag size="small" type="primary" style="white-space: nowrap;">
                      {{ item.floorCount || item.floors }}层楼
                    </el-tag>
                  </div>
                </div>
              </template>
              <div class="building-content">
                <div class="building-info">
                  <p class="building-floors">楼层数: {{ item.floorCount || item.floors }}层</p>
                  <p class="building-address" v-if="item.address">地址: {{ item.address }}</p>
                  <p class="building-description" v-if="item.description">描述: {{ item.description }}</p>
                  <p class="building-time">创建时间: {{ formatDate(item.createTime) }}</p>
                </div>
                <div class="building-actions">
                  <el-button
                    type="primary"
                    @click.stop="handleBuildingClick(item)"
                  >
                    <Icon icon="ep:view" class="mr-5px" /> 查看房间
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    @click.stop="handleEdit(item)"
                  >
                    <Icon icon="ep:edit" class="mr-5px" /> 编辑
                  </el-button>
                  <el-button
                    type="danger"
                    link
                    @click.stop="handleDelete(item)"
                  >
                    <Icon icon="ep:delete" class="mr-5px" /> 删除
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 分页 -->
        <div class="pagination-container" v-if="list.length > 0">
          <el-pagination
            v-model:current-page="queryParams.pageNo"
            :page-size="queryParams.pageSize"
            :total="total"
            layout="total, prev, pager, next, jumper"
            @current-change="handleCurrentChange"
          />
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && list.length === 0" class="empty-state">
          <el-empty description="暂无楼宇数据" />
        </div>
      </div>
    </ContentWrap>
  </div>

  <!-- 表单弹窗：新增/编辑楼宇 -->
  <BuildingForm ref="formRef" @success="getList" />


</template>

<script setup lang="ts">
import { BuildingService } from '@/api/institution/building'
import { formatDate } from '@/utils/formatTime'
import BuildingForm from './BuildingForm.vue'

/** 楼宇管理 */
defineOptions({ name: 'BuildingManagement' })

const message = useMessage() // 消息弹窗
const router = useRouter() // 路由

const loading = ref(true) // 加载中
const list = ref<any[]>([]) // 楼宇列表数据
const total = ref(0) // 总数

/** 查询参数 */
const queryParams = ref({
  pageNo: 1,
  pageSize: 8, // 每页显示8个卡片（2行x4列）
  name: undefined
})

/** 查询楼宇列表 */
const getList = async () => {
  loading.value = true
  try {
    // 获取楼宇列表
    const data = await BuildingService.getBuildingList(queryParams.value)
    list.value = data.list || data
    total.value = data.total || 0
  } catch (error) {
    console.error('获取楼宇列表失败:', error)
    message.error('获取楼宇列表失败')
  } finally {
    loading.value = false
  }
}

/** 点击楼宇卡片 */
const handleBuildingClick = (building: any) => {
  // 跳转到楼层列表页面，传递楼宇ID
  router.push({
    path: '/institution/building/floors',
    query: {
      buildingId: building.id,
      buildingName: building.name
    }
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 8,
    name: undefined
  }
  handleQuery()
}

/** 处理页码变化 */
const handleCurrentChange = (val: number) => {
  queryParams.value.pageNo = val
  getList()
}

/** 新增操作 */
const formRef = ref()
const handleAdd = () => {
  formRef.value.open('create')
}

/** 编辑操作 */
const handleEdit = (row: any) => {
  formRef.value.open('update', row.id)
}

/** 删除操作 */
const handleDelete = async (row: any) => {
  try {
    // 二次确认
    await message.delConfirm()
    // 发起删除
    await BuildingService.deleteBuilding(row.id)
    message.success('删除楼宇成功')
    // 刷新列表
    await getList()
  } catch (error) {
    console.error('删除楼宇失败:', error)
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.building-management {
  .filter-container {
    margin-bottom: 20px;
  }

  .building-list {
    .mb-20 {
      margin-bottom: 20px;
    }

    .building-card {
      min-height: 280px; /* 增加最小高度，确保有足够空间显示内容 */
      cursor: pointer;
      transition: all 0.3s;
      border: 2px solid transparent;
      display: flex;
      flex-direction: column;
      width: 100%; /* 确保卡片宽度为100% */
      box-sizing: border-box; /* 确保边框不会增加宽度 */
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 2px 15px 0 rgba(0, 0, 0, 0.1);
        border-color: var(--el-color-primary);
      }

      .card-header {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .building-name {
          font-weight: bold;
          font-size: 18px;
          width: 100%;
          margin-bottom: 8px;
          word-break: break-word; /* 允许在任何字符间换行 */
          line-height: 1.3;
          color: #303133;
        }

        .building-badges {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap; /* 防止标签换行 */
          gap: 5px;
          width: 100%;
          justify-content: flex-start;
        }
      }

      .building-content {
        padding: 10px 0;
        flex: 1;
        display: flex;
        flex-direction: column;

        .building-info {
          margin-bottom: 15px;
          flex: 1;

          p {
            margin: 8px 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.4;
          }

          .building-floors {
            color: #409EFF;
            font-weight: 500;
            font-size: 16px;
          }

          .building-address {
            color: #67C23A;
            font-weight: 500;
          }

          .building-description {
            color: #909399;
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .building-time {
            color: #C0C4CC;
            font-size: 12px;
          }
        }

        .building-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;
          gap: 8px;

          .el-button {
            flex: 1;

            &:first-child {
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  .pagination-container {
    margin-top: 30px;
    display: flex;
    justify-content: center;
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }
}

.el-tag {
  margin-right: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.mr-5px {
  margin-right: 5px;
}
</style>
