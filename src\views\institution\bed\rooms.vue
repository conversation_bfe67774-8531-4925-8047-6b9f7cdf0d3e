<template>
  <div class="room-management">
    <!-- 面包屑导航 -->
    <ContentWrap>
      <div class="breadcrumb-container">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            <el-button type="primary" link @click="goBack" class="breadcrumb-button">
              <Icon icon="ep:arrow-left" class="mr-5px" /> 返回楼层列表
            </el-button>
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{ buildingName }} - 第{{ currentFloor }}层 - 房间列表</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </ContentWrap>

    <ContentWrap>
      <!-- 搜索工作栏 -->
      <div class="filter-container">
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="80px"
        >
          <el-form-item label="楼层" prop="floor">
            <el-select
              v-model="queryParams.floor"
              placeholder="请选择楼层"
              clearable
              class="!w-240px"
              @change="handleFloorChange"
            >
              <el-option
                v-for="floor in floorList"
                :key="floor"
                :label="floor + '层'"
                :value="floor"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="房间号" prop="roomNumber">
            <el-input
              v-model="queryParams.roomNumber"
              placeholder="请输入房间号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="房间类型" prop="type">
            <el-select
              v-model="queryParams.type"
              placeholder="请选择房间类型"
              clearable
              class="!w-240px"
            >
              <el-option label="单人间" :value="1" />
              <el-option label="双人间" :value="2" />
              <el-option label="活动室" :value="3" />
              <el-option label="餐厅" :value="4" />
              <el-option label="医疗室" :value="5" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button type="primary" @click="handleAdd">
              <Icon icon="ep:plus" class="mr-5px" /> 新增房间
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </ContentWrap>

    <!-- 房间卡片列表 -->
    <ContentWrap>
      <div class="room-list" v-loading="loading">
        <el-row :gutter="20">
          <el-col
            v-for="item in list"
            :key="item.id"
            :span="6"
            class="mb-20"
          >
            <el-card
              :class="['room-card']"
              shadow="hover"
              @click="handleRoomClick(item)"
            >
              <template #header>
                <div class="card-header">
                  <el-tooltip :content="item.roomNumber" placement="top" :show-after="500">
                    <span class="room-name">{{ item.roomNumber }}</span>
                  </el-tooltip>
                  <div class="room-badges">
                    <el-tag size="small" :type="getRoomTypeTag(item.type)" style="white-space: nowrap;">
                      {{ getRoomTypeName(item.type) }}
                    </el-tag>
                    <el-tag
                      size="small"
                      :type="getRoomStatusType(item.status)"
                      class="ml-5"
                      style="white-space: nowrap;"
                    >
                      {{ getRoomStatusName(item.status) }}
                    </el-tag>
                  </div>
                </div>
              </template>
              <div class="room-content">
                <div class="room-info">
                  <p class="room-floor">楼层: {{ item.floor }}层</p>
                  <p class="room-capacity">房间容量: {{ item.capacity }}人</p>
                  <p class="room-beds">床位数量: {{ item.bedCount }}张</p>
                  <p class="room-facilities" v-if="item.facilities">设备: {{ item.facilities }}</p>
                  <p class="room-description" v-if="item.description">描述: {{ item.description }}</p>
                </div>
                <div class="room-actions">
                  <el-button
                    type="primary"
                    link
                    @click="handleEdit(item)"
                  >
                    <Icon icon="ep:edit" class="mr-5px" /> 编辑
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    @click="handleView(item)"
                  >
                    <Icon icon="ep:view" class="mr-5px" /> 查看
                  </el-button>
                  <el-button
                    type="danger"
                    link
                    @click="handleDelete(item)"
                  >
                    <Icon icon="ep:delete" class="mr-5px" /> 删除
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>



        <!-- 空状态 -->
        <div v-if="!loading && list.length === 0" class="empty-state">
          <el-empty description="该楼宇暂无房间数据" />
        </div>
      </div>
    </ContentWrap>
  </div>

  <!-- 表单弹窗：编辑 -->
  <RoomForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { RoomService } from '@/api/institution/room'
import { BuildingService } from '@/api/institution/building'
import RoomForm from './RoomForm.vue'

/** 房间管理 */
defineOptions({ name: 'RoomManagement' })

const message = useMessage() // 消息弹窗
const router = useRouter() // 路由
const route = useRoute() // 当前路由

const loading = ref(true) // 加载中
const list = ref<any[]>([]) // 列表数据
const floorList = ref<number[]>([]) // 楼层列表
const total = ref(0) // 总数

// 从路由参数获取楼宇信息
const buildingId = ref(Number(route.query.buildingId) || 0)
const buildingName = ref((route.query.buildingName as string) || '未知楼宇')
const currentFloor = ref(Number(route.query.floor) || 1)

/** 查询参数 */
const queryParams = ref({
  pageNo: 1,
  pageSize: 8, // 每页显示8个卡片（2行x4列）
  buildingId: buildingId.value,
  floor: currentFloor.value, // 默认显示当前楼层
  roomNumber: undefined as string | undefined,
  type: undefined as number | undefined
})

/** 查询房间列表 */
const getList = async () => {
  loading.value = true
  try {
    // 获取房间分页列表
    const data = await RoomService.getRoomList(queryParams.value)
    // 处理分页数据
    if (data && typeof data === 'object' && 'list' in data) {
      // 分页数据格式
      list.value = data.list || []
      total.value = data.total || 0
    } else if (Array.isArray(data)) {
      // 数组格式（兼容旧版本）
      list.value = data
      total.value = data.length
    } else {
      // 其他格式
      list.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取房间列表失败:', error)
    message.error('获取房间列表失败')
    list.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/** 获取楼层列表 */
const getFloorList = async () => {
  if (buildingId.value) {
    try {
      const building = await BuildingService.getBuilding(buildingId.value)
      const floorCount = building.floors || building.floorCount || 0
      floorList.value = Array.from({ length: floorCount }, (_, i) => i + 1)
    } catch (error) {
      console.error('获取楼层列表失败:', error)
      message.error('获取楼层列表失败')
    }
  }
}

/** 楼层变更 */
const handleFloorChange = () => {
  // 楼层变更时重新加载房间列表
  getList()
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 8,
    buildingId: buildingId.value,
    floor: currentFloor.value, // 保持当前楼层
    roomNumber: undefined as string | undefined,
    type: undefined as number | undefined
  }
  handleQuery()
}

/** 返回楼层列表 */
const goBack = () => {
  // 返回到楼层列表页面
  router.push({
    path: '/institution/building/floors',
    query: {
      buildingId: buildingId.value,
      buildingName: buildingName.value
    }
  })
}

/** 获取房间类型标签样式 */
const getRoomTypeTag = (type: number) => {
  const typeMap = {
    1: 'success',  // 单人间
    2: 'warning',  // 双人间
    3: 'info',     // 活动室
    4: 'danger',   // 餐厅
    5: 'primary'   // 医疗室
  }
  return typeMap[type] || ''
}

/** 获取房间类型名称 */
const getRoomTypeName = (type: number) => {
  const typeMap = {
    1: '单人间',
    2: '双人间',
    3: '活动室',
    4: '餐厅',
    5: '医疗室'
  }
  return typeMap[type] || '未知类型'
}

/** 获取房间状态名称 */
const getRoomStatusName = (status: number) => {
  const statusMap = {
    1: '正常',
    2: '维修中'
  }
  return statusMap[status] || '未知状态'
}

/** 获取房间状态标签类型 */
const getRoomStatusType = (status: number) => {
  const statusMap = {
    1: 'success', // 正常
    2: 'danger'   // 维修中
  }
  return statusMap[status] || 'info'
}

/** 新增操作 */
const formRef = ref()
const handleAdd = () => {
  // 新增时预设楼宇ID
  formRef.value.open('create', undefined, buildingId.value)
}

/** 编辑操作 */
const handleEdit = (row: any) => {
  formRef.value.open('update', row.id)
}

/** 查看操作 */
const handleView = (row: any) => {
  formRef.value.open('view', row.id)
}

/** 删除操作 */
const handleDelete = async (row: any) => {
  try {
    // 二次确认
    await message.delConfirm()
    // 发起删除
    await RoomService.deleteRoom(row.id)
    message.success('删除房间成功')
    // 刷新列表
    await getList()
  } catch (error) {
    console.error('删除房间失败:', error)
  }
}

/** 点击房间卡片 */
const handleRoomClick = (room: any) => {
  // 跳转到床铺管理页面，传递房间ID和相关信息
  router.push({
    path: '/institution/bed/beds',
    query: {
      roomId: room.id,
      roomNumber: room.roomNumber,
      buildingId: room.buildingId,
      buildingName: room.buildingName,
      floor: room.floor
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getFloorList()
  getList()
})
</script>

<style lang="scss" scoped>
.room-management {
  .breadcrumb-container {
    margin-bottom: 20px;

    .el-breadcrumb {
      font-size: 16px;

      .el-breadcrumb-item {
        font-size: 16px;
      }

      .breadcrumb-button {
        padding: 0;
        height: auto;
        line-height: 1;
        vertical-align: baseline;
        font-size: 16px;

        .mr-5px {
          margin-right: 5px;
        }
      }
    }
  }

  .filter-container {
    margin-bottom: 20px;
  }

  .room-list {
    .mb-20 {
      margin-bottom: 20px;
    }

    .room-card {
      min-height: 280px; /* 增加最小高度，确保有足够空间显示内容 */
      cursor: pointer;
      transition: all 0.3s;
      border: 2px solid transparent;
      display: flex;
      flex-direction: column;
      width: 100%; /* 确保卡片宽度为100% */
      box-sizing: border-box; /* 确保边框不会增加宽度 */
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 2px 15px 0 rgba(0, 0, 0, 0.1);
        border-color: var(--el-color-primary);
      }

      .card-header {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .room-name {
          font-weight: bold;
          font-size: 18px;
          width: 100%;
          margin-bottom: 8px;
          word-break: break-word; /* 允许在任何字符间换行 */
          line-height: 1.3;
          color: #303133;
        }

        .room-badges {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap; /* 防止标签换行 */
          gap: 5px;
          width: 100%;
          justify-content: flex-start;
        }
      }

      .room-content {
        padding: 10px 0;
        flex: 1;
        display: flex;
        flex-direction: column;

        .room-info {
          margin-bottom: 15px;
          flex: 1;

          p {
            margin: 8px 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.4;
          }

          .room-floor {
            color: #409EFF;
            font-weight: 500;
          }

          .room-capacity {
            color: #67C23A;
          }

          .room-beds {
            color: #E6A23C;
          }

          .room-facilities {
            color: #909399;
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .room-description {
            color: #909399;
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .room-actions {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          margin-top: auto;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 30px;
    display: flex;
    justify-content: center;
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }
}

.el-tag {
  margin-right: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.mr-5px {
  margin-right: 5px;
}
</style>
